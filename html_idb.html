<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="utf-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1" />
  <title>InnoDB (MySQL) notes + IndexedDB Viewer</title>
  <style>
    :root { --bg:#0b1020; --panel:#131936; --accent:#6aa6ff; --text:#e6ecff; --muted:#9db0d1; --danger:#ff6a6a; }
    * { box-sizing: border-box; }
    body { margin:0; font:14px/1.4 system-ui, -apple-system, Segoe UI, Roboto, Ubuntu, Cantarell, Noto Sans, Helvetica, Arial, "Apple Color Emoji", "Segoe UI Emoji"; background:var(--bg); color:var(--text); }
    header { padding:16px 20px; border-bottom:1px solid #243; display:flex; gap:12px; align-items:center; justify-content:space-between; background:linear-gradient(180deg, #0f1533, #0c1330); position:sticky; top:0; z-index:10; }
    header h1 { margin:0; font-size:18px; letter-spacing:0.3px; }
    .wrap { display:grid; grid-template-columns: 320px 1fr; gap:12px; padding:12px; }
    .card { background:var(--panel); border:1px solid #1d264b; border-radius:14px; box-shadow:0 6px 22px rgba(0,0,0,.25); overflow:hidden; }
    .card h2 { margin:0; padding:10px 14px; font-size:13px; color:var(--muted); border-bottom:1px solid #1d264b; background:#0e1531; text-transform:uppercase; letter-spacing:.08em; }
    .card .body { padding:12px; }
    .row { display:flex; gap:8px; align-items:center; flex-wrap:wrap; }
    input, select, button, textarea { background:#0f183a; color:var(--text); border:1px solid #2a3a77; border-radius:10px; padding:8px 10px; outline:none; }
    input::placeholder, textarea::placeholder { color:#6e83b3; }
    button { cursor:pointer; white-space:nowrap; }
    button.primary { background:var(--accent); color:#06122e; border-color:transparent; font-weight:600; }
    button.ghost { background:transparent; }
    button.danger { background:transparent; border-color:#5a2030; color:var(--danger); }
    .list { display:flex; flex-direction:column; gap:8px; max-height:42vh; overflow:auto; }
    .item { padding:8px 10px; border:1px solid #22305d; border-radius:10px; display:flex; justify-content:space-between; gap:8px; align-items:center; }
    .item small { color:var(--muted); }
    .grid { display:grid; grid-template-columns: 1fr 1fr; gap:12px; }
    table { width:100%; border-collapse:collapse; }
    th, td { padding:8px 10px; border-bottom:1px solid #22305d; text-align:left; vertical-align:top; }
    th { position:sticky; top:0; background:#0e1531; z-index:1; }
    .toolbar { display:flex; gap:8px; flex-wrap:wrap; padding-bottom:8px; border-bottom:1px solid #1d264b; margin-bottom:8px; }
    .muted { color:var(--muted); }
    details { background:#0f183a; border:1px solid #22305d; padding:8px; border-radius:10px; }
    summary { cursor:pointer; color:var(--muted); }
    pre { white-space:pre-wrap; word-break:break-word; margin:0; }
    .mono { font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace; }
    .pill { padding:4px 8px; border:1px solid #2a3a77; border-radius:999px; font-size:12px; color:var(--muted); }
    .footer { padding:10px; font-size:12px; color:var(--muted); text-align:center; }
  </style>
</head>
<body>
  <header>
    <h1>InnoDB (MySQL) notes + IndexedDB Viewer</h1>
    <div class="row">
      <button id="refreshDbs">Refresh DB list</button>
      <button class="ghost" id="createDb">Create / Open DB…</button>
      <button class="danger" id="deleteDb">Delete DB…</button>
    </div>
  </header>

  <div class="wrap">
    <section class="card" style="grid-column: 1 / -1">
      <h2>InnoDB (MySQL) Quick Notes</h2>
      <div class="body">
        <p class="muted">In this page, “idb” refers to MySQL’s InnoDB storage engine (XAMPP). Below is an in‑browser IndexedDB viewer unrelated to MySQL.</p>
        <details open>
          <summary>Common checks</summary>
          <pre class="mono">-- Check available engines and whether InnoDB is enabled
SHOW ENGINES;

-- Check current default storage engine
SELECT @@default_storage_engine;

-- View InnoDB status summary
SHOW /*!50000 ENGINE*/ INNODB STATUS; -- may require sufficient privileges</pre>
        </details>
        <details style="margin-top:10px">
          <summary>Create an InnoDB table</summary>
          <pre class="mono">CREATE DATABASE IF NOT EXISTS demo_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE demo_db;

CREATE TABLE IF NOT EXISTS products (
  id INT UNSIGNED NOT NULL AUTO_INCREMENT,
  name VARCHAR(120) NOT NULL,
  price DECIMAL(10,2) NOT NULL DEFAULT 0,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;</pre>
        </details>
        <details style="margin-top:10px">
          <summary>phpMyAdmin (XAMPP) quick tips</summary>
          <ul class="muted" style="margin:8px 0 0 16px">
            <li>Check engines: phpMyAdmin → Server → Status/Variables → filter “storage” or “InnoDB”.</li>
            <li>Default engine: run SELECT @@default_storage_engine; in SQL tab.</li>
            <li>Table engine: browse a table → Structure → check “Storage Engine”.</li>
          </ul>
        </details>
      </div>
    </section>
    <section class="card" id="dbPanel">
      <h2>Databases</h2>
      <div class="body">
        <div class="row" style="margin-bottom:8px">
          <span class="pill" id="dbSupport"></span>
        </div>
        <div class="list" id="dbList"></div>
      </div>
    </section>

    <section class="card">
      <h2 id="currentDbTitle">No database selected</h2>
      <div class="body">
        <div class="toolbar">
          <select id="storeSelect" disabled></select>
          <button id="openStore" disabled>Open store</button>
          <button id="newStore" disabled>New store…</button>
          <button id="deleteStore" class="danger" disabled>Delete store</button>
          <button id="clearStore" class="danger" disabled>Clear store</button>
          <span class="pill" id="dbMeta"></span>
        </div>

        <div class="grid">
          <div class="card">
            <h2>Records</h2>
            <div class="body">
              <div class="toolbar">
                <input id="queryInput" placeholder="Filter (substring in key or JSON)" />
                <input id="limitInput" type="number" min="1" value="200" style="width:120px" />
                <button id="reloadStore">Reload</button>
                <button id="exportStore">Export store (JSON)</button>
                <label class="pill" for="importFile">Import JSON<input id="importFile" type="file" accept="application/json" style="display:none" /></label>
              </div>
              <div style="max-height:52vh; overflow:auto">
                <table id="recordsTable">
                  <thead><tr><th style="width:28%">Key</th><th>Value</th><th style="width:90px">Actions</th></tr></thead>
                  <tbody></tbody>
                </table>
              </div>
            </div>
          </div>

          <div class="card">
            <h2>Inspector</h2>
            <div class="body">
              <details open>
                <summary>Selected item</summary>
                <pre class="mono" id="inspector">Select a row to inspect…</pre>
              </details>
              <details style="margin-top:10px">
                <summary>Add / Update item</summary>
                <div class="row" style="margin:8px 0">
                  <input id="editKey" placeholder="Key (omit for auto)" style="width:40%" />
                  <select id="keyType">
                    <option value="auto">auto</option>
                    <option value="string">string</option>
                    <option value="number">number</option>
                  </select>
                  <button id="putBtn" class="primary">Put</button>
                </div>
                <textarea id="editValue" rows="10" placeholder='Value as JSON (e.g. {"a":1})' style="width:100%"></textarea>
              </details>
            </div>
          </div>
        </div>
      </div>
    </section>
  </div>

  <div class="footer">Tip: Some browsers restrict <code>indexedDB.databases()</code>. If DBs don't list, use “Create / Open DB…” to open by name.</div>

<script>
(function(){
  const $ = sel => document.querySelector(sel);
  const dbList = $('#dbList');
  const dbSupport = $('#dbSupport');
  const currentDbTitle = $('#currentDbTitle');
  const storeSelect = $('#storeSelect');
  const dbMeta = $('#dbMeta');
  const openStoreBtn = $('#openStore');
  const newStoreBtn = $('#newStore');
  const deleteStoreBtn = $('#deleteStore');
  const clearStoreBtn = $('#clearStore');
  const recordsTableBody = document.querySelector('#recordsTable tbody');
  const inspector = $('#inspector');
  const queryInput = $('#queryInput');
  const limitInput = $('#limitInput');

  const reloadStoreBtn = $('#reloadStore');
  const exportStoreBtn = $('#exportStore');
  const importFile = $('#importFile');

  const editKey = $('#editKey');
  const keyType = $('#keyType');
  const editValue = $('#editValue');
  const putBtn = $('#putBtn');

  let currentDB = null; // IDBDatabase
  let currentStoreName = null;

  function setDbUIEnabled(enabled) {
    [storeSelect, openStoreBtn, newStoreBtn, deleteStoreBtn, clearStoreBtn].forEach(el => el.disabled = !enabled);
  }

  function setStoreUIEnabled(enabled){
    [reloadStoreBtn, exportStoreBtn, importFile, putBtn].forEach(el => el.disabled = !enabled);
  }

  // Helpers
  function promisifyRequest(req) {
    return new Promise((resolve, reject) => {
      req.onsuccess = () => resolve(req.result);
      req.onerror = () => reject(req.error);
    });
  }

  async function listDatabases(){
    if (!('indexedDB' in window)) {
      dbSupport.textContent = 'IndexedDB not supported in this browser';
      dbSupport.style.color = 'var(--danger)';
      return [];
    }
    dbSupport.textContent = 'IndexedDB supported';
    try {
      if (indexedDB.databases) {
        const dbs = await indexedDB.databases();
        return dbs.map(d => ({name: d.name, version: d.version})).filter(d => d.name);
      }
      return [];
    } catch (e) {
      console.warn('databases() not available:', e);
      return [];
    }
  }

  function renderDbList(dbs){
    dbList.innerHTML = '';
    if (!dbs.length) {
      const div = document.createElement('div');
      div.className = 'muted';
      div.textContent = 'No databases detected (use Create/Open).';
      dbList.appendChild(div);
      return;
    }
    dbs.forEach(db => {
      const item = document.createElement('div');
      item.className = 'item';
      const left = document.createElement('div');
      left.innerHTML = `<strong>${db.name}</strong><div class="muted">v${db.version||1}</div>`;
      const right = document.createElement('div');
      const btn = document.createElement('button');
      btn.textContent = 'Open';
      btn.onclick = () => openDatabase(db.name, db.version||1);
      right.appendChild(btn);
      item.appendChild(left); item.appendChild(right);
      dbList.appendChild(item);
    });
  }

  async function refreshDbList(){
    renderDbList(await listDatabases());
  }

  function openIDB(name, version){
    return new Promise((resolve, reject) => {
      const req = indexedDB.open(name, version);
      req.onupgradeneeded = (ev) => {
        // Don't auto-create stores here; let user do it explicitly.
        console.log('Upgrade needed', ev);
      };
      req.onsuccess = () => resolve(req.result);
      req.onerror = () => reject(req.error);
    });
  }

  async function openDatabase(name, version){
    try {
      if (currentDB) { currentDB.close(); }
      currentDB = await openIDB(name, version);
      currentDB.onversionchange = () => {
        alert('Database version change detected. Reloading…');
        location.reload();
      };
      currentDbTitle.textContent = `DB: ${currentDB.name}`;
      dbMeta.textContent = `version ${currentDB.version} • stores: ${currentDB.objectStoreNames.length}`;
      setDbUIEnabled(true);
      // Populate store select
      storeSelect.innerHTML = '';
      for (let i=0;i<currentDB.objectStoreNames.length;i++){
        const opt = document.createElement('option');
        opt.value = opt.textContent = currentDB.objectStoreNames[i];
        storeSelect.appendChild(opt);
      }
      currentStoreName = storeSelect.value || null;
      setStoreUIEnabled(!!currentStoreName);
    } catch (e) {
      console.error(e);
      alert('Failed to open DB: ' + e.message);
    }
  }

  function withTx(storeName, mode, fn){
    return new Promise((resolve, reject) => {
      try {
        const tx = currentDB.transaction(storeName, mode);
        const store = tx.objectStore(storeName);
        const res = fn(store, tx);
        tx.oncomplete = () => resolve(res);
        tx.onerror = () => reject(tx.error);
        tx.onabort = () => reject(tx.error || new Error('Transaction aborted'));
      } catch (e) {
        reject(e);
      }
    });
  }

  async function readAll(storeName, query, limit){
    const out = [];
    await withTx(storeName, 'readonly', (store) => {
      const req = store.openCursor();
      req.onsuccess = () => {
        const cur = req.result;
        if (cur) {
          let include = true;
          if (query) {
            try {
              const valStr = JSON.stringify(cur.value);
              const keyStr = typeof cur.key === 'object' ? JSON.stringify(cur.key) : String(cur.key);
              include = keyStr.includes(query) || valStr.includes(query);
            } catch {}
          }
          if (include) out.push({key: cur.key, value: cur.value});
          if (!limit || out.length < limit) cur.continue();
        }
      };
    });
    return out;
  }

  function renderRows(rows){
    recordsTableBody.innerHTML = '';
    if (!rows.length) {
      const tr = document.createElement('tr');
      const td = document.createElement('td');
      td.colSpan = 3; td.className = 'muted'; td.textContent = 'No records';
      tr.appendChild(td); recordsTableBody.appendChild(tr);
      return;
    }
    rows.forEach(({key, value}, idx) => {
      const tr = document.createElement('tr');
      const tdKey = document.createElement('td');
      tdKey.className = 'mono';
      tdKey.textContent = tryStringify(key);
      const tdVal = document.createElement('td');
      tdVal.className = 'mono';
      tdVal.innerHTML = '<pre>'+escapeHtml(tryStringify(value))+'</pre>';
      const tdAct = document.createElement('td');
      const viewBtn = document.createElement('button'); viewBtn.textContent='Inspect';
      viewBtn.onclick = () => inspector.textContent = pretty(value);
      const delBtn = document.createElement('button'); delBtn.textContent='Delete'; delBtn.className='danger';
      delBtn.onclick = async () => { if (confirm('Delete this record?')) { await deleteKey(currentStoreName, key); await reload(); } };
      tdAct.appendChild(viewBtn); tdAct.appendChild(delBtn);
      tr.append(tdKey, tdVal, tdAct); recordsTableBody.appendChild(tr);
    });
  }

  function tryStringify(v){
    try { return typeof v === 'string' ? v : JSON.stringify(v); } catch { return String(v); }
  }
  function pretty(v){ try { return JSON.stringify(v, null, 2); } catch { return String(v); } }
  function escapeHtml(s){ return s.replace(/[&<>]/g, c => ({'&':'&amp;','<':'&lt;','>':'&gt;'}[c])); }

  async function reload(){
    if (!currentStoreName) return;
    const q = queryInput.value.trim();
    const lim = parseInt(limitInput.value,10) || 200;
    const rows = await readAll(currentStoreName, q, lim);
    renderRows(rows);
  }

  async function exportStore(){
    const rows = await readAll(currentStoreName, '', 0);
    const blob = new Blob([JSON.stringify(rows, null, 2)], {type:'application/json'});
    const a = document.createElement('a');
    a.href = URL.createObjectURL(blob);
    a.download = `${currentDB.name}__${currentStoreName}.json`;
    a.click();
  }

  async function importJsonFile(file){
    const text = await file.text();
    const rows = JSON.parse(text);
    await withTx(currentStoreName, 'readwrite', (store) => {
      rows.forEach(r => {
        if (r && 'key' in r) store.put(r.value, r.key);
        else store.put(r);
      });
    });
    await reload();
  }

  async function putItem(){
    let key = editKey.value.trim();
    let value;
    try { value = JSON.parse(editValue.value || 'null'); }
    catch(e){ alert('Value must be valid JSON: ' + e.message); return; }
    if (keyType.value === 'number' && key) key = Number(key);
    try {
      await withTx(currentStoreName, 'readwrite', (store) => {
        if (!key || keyType.value === 'auto') store.put(value);
        else store.put(value, key);
      });
      editKey.value = ''; editValue.value = '';
      await reload();
    } catch(e){ alert('Put failed: ' + e.message); }
  }

  async function deleteKey(storeName, key){
    await withTx(storeName, 'readwrite', (store)=>store.delete(key));
  }

  // Store operations
  async function createStore(){
    const name = prompt('New object store name:');
    if (!name) return;
    const keyPath = prompt('Key path? (leave empty for key generator)') || undefined;
    const autoInc = keyPath ? false : confirm('Use auto-increment keys?');
    const newVersion = currentDB.version + 1;
    currentDB.close();
    const req = indexedDB.open(currentDB.name, newVersion);
    req.onupgradeneeded = () => {
      req.result.createObjectStore(name, { keyPath: keyPath || undefined, autoIncrement: !!autoInc });
    };
    req.onsuccess = async () => {
      currentDB = req.result;
      await openDatabase(currentDB.name, currentDB.version);
    };
    req.onerror = () => alert('Create store failed: ' + req.error);
  }

  async function deleteStore(){
    if (!currentStoreName) return;
    if (!confirm(`Delete store "${currentStoreName}"?`)) return;
    const newVersion = currentDB.version + 1;
    const nameToDelete = currentStoreName;
    currentDB.close();
    const req = indexedDB.open(currentDB.name, newVersion);
    req.onupgradeneeded = () => {
      const db = req.result;
      db.deleteObjectStore(nameToDelete);
    };
    req.onsuccess = async () => {
      currentDB = req.result;
      await openDatabase(currentDB.name, currentDB.version);
    };
    req.onerror = () => alert('Delete store failed: ' + req.error);
  }

  async function clearStore(){
    if (!currentStoreName) return;
    if (!confirm(`Clear all records in "${currentStoreName}"?`)) return;
    await withTx(currentStoreName, 'readwrite', (store) => store.clear());
    await reload();
  }

  // DB management actions
  $('#refreshDbs').onclick = refreshDbList;
  $('#createDb').onclick = async () => {
    const name = prompt('Database name to open/create:');
    if (!name) return;
    let verStr = prompt('Version (number, leave blank for default/current):') || '';
    let ver = parseInt(verStr,10);
    if (!ver || isNaN(ver)) ver = undefined; // Let browser decide
    try {
      const req = indexedDB.open(name, ver);
      req.onupgradeneeded = () => { /* no-op: user can add stores after */ };
      req.onsuccess = async () => { currentDB = req.result; await openDatabase(currentDB.name, currentDB.version); };
      req.onerror = () => alert('Open/create failed: ' + req.error);
    } catch(e){ alert('Open failed: ' + e.message); }
  };
  $('#deleteDb').onclick = async () => {
    const name = prompt('Database name to delete:');
    if (!name) return;
    const ok = confirm(`Delete database "${name}"? This cannot be undone.`);
    if (!ok) return;
    const delReq = indexedDB.deleteDatabase(name);
    delReq.onsuccess = delReq.onerror = delReq.onblocked = () => refreshDbList();
  };

  // Store buttons
  openStoreBtn.onclick = async () => { currentStoreName = storeSelect.value; setStoreUIEnabled(!!currentStoreName); await reload(); };
  newStoreBtn.onclick = createStore;
  deleteStoreBtn.onclick = deleteStore;
  clearStoreBtn.onclick = clearStore;

  // Records toolbar
  reloadStoreBtn.onclick = reload;
  exportStoreBtn.onclick = exportStore;
  importFile.onchange = e => { if (e.target.files[0]) importJsonFile(e.target.files[0]); };

  // Put/edit
  putBtn.onclick = putItem;

  // Store select change
  storeSelect.onchange = () => { currentStoreName = storeSelect.value; setStoreUIEnabled(!!currentStoreName); };

  // Initial
  (async function init(){
    await refreshDbList();
  })();
})();
</script>
</body>
</html>
