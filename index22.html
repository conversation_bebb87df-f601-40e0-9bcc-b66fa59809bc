<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI Chatbot</title>
    <style>
        :root {
            --primary-color: #4f46e5;
            --secondary-color: #818cf8;
            --background-color: #f9fafb;
            --chat-bg: #ffffff;
            --user-bubble: #e0e7ff;
            --bot-bubble: #f3f4f6;
            --text-color: #1f2937;
            --placeholder-color: #9ca3af;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        body {
            background-color: var(--background-color);
            color: var(--text-color);
            height: 100vh;
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }

        .container {
            max-width: 1200px;
            width: 100%;
            margin: 0 auto;
            padding: 20px;
            display: flex;
            flex-direction: column;
            height: 100%;
        }

        header {
            text-align: center;
            padding: 20px 0;
            border-bottom: 1px solid #e5e7eb;
            margin-bottom: 20px;
        }

        header h1 {
            font-size: 2rem;
            color: var(--primary-color);
            margin-bottom: 10px;
        }

        header p {
            color: var(--placeholder-color);
            font-size: 1rem;
        }

        .chat-container {
            flex: 1;
            background-color: var(--chat-bg);
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }

        .chat-messages {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
            display: flex;
            flex-direction: column;
            gap: 15px;
        }

        .message {
            max-width: 80%;
            padding: 12px 16px;
            border-radius: 18px;
            line-height: 1.5;
            position: relative;
            animation: fadeIn 0.3s ease-in-out;
        }
        
        /* Markdown styling */
        .message h1, .message h2, .message h3, .message h4, .message h5, .message h6 {
            margin-top: 16px;
            margin-bottom: 8px;
            font-weight: 600;
            line-height: 1.25;
        }
        
        .message h1 {
            font-size: 1.5em;
            border-bottom: 1px solid #eaecef;
            padding-bottom: 0.3em;
        }
        
        .message h2 {
            font-size: 1.3em;
            border-bottom: 1px solid #eaecef;
            padding-bottom: 0.3em;
        }
        
        .message h3 {
            font-size: 1.2em;
        }
        
        .message h4 {
            font-size: 1.1em;
        }
        
        .message h5, .message h6 {
            font-size: 1em;
        }
        
        .message code {
            font-family: 'Courier New', Courier, monospace;
            background-color: rgba(0, 0, 0, 0.05);
            padding: 0.2em 0.4em;
            border-radius: 3px;
            font-size: 0.9em;
        }
        
        .message pre {
            background-color: rgba(0, 0, 0, 0.05);
            border-radius: 6px;
            padding: 16px;
            overflow: auto;
            margin: 16px 0;
        }
        
        .message pre code {
            background-color: transparent;
            padding: 0;
            font-size: 0.9em;
            white-space: pre;
            overflow-wrap: normal;
            border-radius: 0;
        }
        
        .message ul, .message ol {
            padding-left: 2em;
            margin: 8px 0;
        }
        
        .message li {
            margin: 4px 0;
        }
        
        .message a {
            color: #0366d6;
            text-decoration: none;
        }
        
        .message a:hover {
            text-decoration: underline;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .user-message {
            align-self: flex-end;
            background-color: var(--user-bubble);
            border-bottom-right-radius: 4px;
            color: var(--primary-color);
        }

        .bot-message {
            align-self: flex-start;
            background-color: var(--bot-bubble);
            border-bottom-left-radius: 4px;
        }

        .message-time {
            font-size: 0.7rem;
            color: var(--placeholder-color);
            margin-top: 5px;
            text-align: right;
        }

        .typing-indicator {
            align-self: flex-start;
            background-color: var(--bot-bubble);
            border-radius: 18px;
            padding: 12px 16px;
            display: none;
        }

        .typing-indicator span {
            height: 8px;
            width: 8px;
            float: left;
            margin: 0 1px;
            background-color: var(--placeholder-color);
            display: block;
            border-radius: 50%;
            opacity: 0.4;
        }

        .typing-indicator span:nth-of-type(1) {
            animation: 1s blink infinite 0.3333s;
        }

        .typing-indicator span:nth-of-type(2) {
            animation: 1s blink infinite 0.6666s;
        }

        .typing-indicator span:nth-of-type(3) {
            animation: 1s blink infinite 0.9999s;
        }

        @keyframes blink {
            50% { opacity: 1; }
        }

        .chat-input-container {
            padding: 20px;
            border-top: 1px solid #e5e7eb;
            display: flex;
            gap: 10px;
        }

        .chat-input {
            flex: 1;
            padding: 12px 20px;
            border: 1px solid #e5e7eb;
            border-radius: 24px;
            font-size: 1rem;
            outline: none;
            transition: border-color 0.3s;
        }

        .chat-input:focus {
            border-color: var(--primary-color);
        }

        .send-button {
            background-color: var(--primary-color);
            color: white;
            border: none;
            border-radius: 24px;
            padding: 12px 24px;
            font-size: 1rem;
            cursor: pointer;
            transition: background-color 0.3s;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
        }

        .send-button:hover {
            background-color: var(--secondary-color);
        }

        .send-button:disabled {
            background-color: #d1d5db;
            cursor: not-allowed;
        }

        .send-icon {
            width: 18px;
            height: 18px;
        }

        /* Responsive design */
        @media (max-width: 768px) {
            .container {
                padding: 10px;
            }

            header {
                padding: 15px 0;
            }

            header h1 {
                font-size: 1.5rem;
            }

            .message {
                max-width: 90%;
            }

            .chat-input-container {
                padding: 15px;
            }

            .send-button {
                padding: 12px 15px;
            }
        }

        /* Dark mode support */
        @media (prefers-color-scheme: dark) {
            :root {
                --background-color: #111827;
                --chat-bg: #1f2937;
                --user-bubble: #4338ca;
                --bot-bubble: #374151;
                --text-color: #f9fafb;
                --placeholder-color: #9ca3af;
            }

            .chat-input {
                background-color: #374151;
                color: var(--text-color);
                border-color: #4b5563;
            }

            .user-message {
                color: white;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <header>
            <h1>GovPSS & IPMS AI Assistant</h1>
            <p>Your guide to Papua New Guinea's government systems</p>
        </header>
        <div class="chat-container">
            <div class="chat-messages" id="chat-messages">
                <div class="message bot-message">
                    Hello! I'm your AI assistant with knowledge about GovPSS and IPMS systems. How can I help you today?
                    <div class="message-time">Just now</div>
                </div>
                <div class="typing-indicator" id="typing-indicator">
                    <span></span>
                    <span></span>
                    <span></span>
                </div>
            </div>
            <div class="chat-input-container">
                <input type="text" class="chat-input" id="chat-input" placeholder="Type your message here..." autocomplete="off">
                <button class="send-button" id="send-button">
                    <span>Send</span>
                    <svg class="send-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8"></path>
                    </svg>
                </button>
            </div>
        </div>
    </div>

    <script>
        // Constants
        const API_KEY = '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';
        const API_URL = 'https://openrouter.ai/api/v1/chat/completions';
        const MODEL = 'deepseek/deepseek-chat-v3-0324:free';

        // DOM Elements
        const chatMessages = document.getElementById('chat-messages');
        const chatInput = document.getElementById('chat-input');
        const sendButton = document.getElementById('send-button');
        const typingIndicator = document.getElementById('typing-indicator');

        // System context from context_info.md
        const systemContext = `Comprehensive System Design Write-Up for GovPSS and IPMS

Overview
This write-up consolidates details from prior discussions about two systems: GovPSS, a Human Resource Management System tailored for government institutions in Papua New Guinea, and IPMS (Integrated Progress Monitoring System), a platform for monitoring human resources, organizational plans, budgets, and asset usage linked to Workplans and Annual Activity Plans. Both systems leverage CodeIgniter 4 and MySQL, with a focus on RESTful architecture, user-friendly interfaces, and role-based functionalities.

GovPSS: Human Resource Management System
Purpose and Context: GovPSS is designed for government organizations, such as the East Sepik Provincial Administration, to streamline HR processes, including job applications, employee management, and administrative tasks.

Key Features:
- Job Application Process with account creation, job listings, application submission with 24-hour revocation period, and administrator-managed pre-selection stages.
- Employee Portal for viewing salaries, benefits, and personal details.
- Administrative Portal with role-based access for managing applications, interviews, and selections.
- Correspondence Tracking for document management and workflows.

Technical Specifications:
- Framework: CodeIgniter 4.6.0 with MVC architecture.
- Database: MySQL with tables for users, organizations, correspondence, and recruitment processes.
- Frontend: Bootstrap 5 for responsive design.

IPMS: Integrated Progress Monitoring System
Purpose and Context: IPMS monitors human resources, organizational plans, budgets, and assets, tied to Workplans and Annual Activity Plans.

Key Features:
- Workplan and Activity Management linked to Budget Books, Corporate Plans, and Development Plans.
- Financial Claim Management with automated form generation (FF3, FF4, Alignment Sheets).
- User Roles: Officer, Group Admin, Supervisor, ARO, Fund Manager, Administrator.
- Button-driven, mobile app-like interface.

Technical Specifications:
- Framework: CodeIgniter 4 with RESTful architecture.
- Database: MySQL with tables for users, structures, plans, and budgets.
- Frontend: Bootstrap 5 for responsive UI.
- Tools: DomPDF for generating PDF forms.

Both systems are tailored for Papua New Guinea's government context, supporting organizations like the East Sepik Provincial Administration.`;

        // Chat history
        let chatHistory = [
            {
                role: 'system',
                content: systemContext
            },
            {
                role: 'assistant',
                content: 'Hello! I\'m your AI assistant with knowledge about GovPSS and IPMS systems. How can I help you today?'
            }
        ];

        // Event Listeners
        sendButton.addEventListener('click', sendMessage);
        chatInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                sendMessage();
            }
        });

        // Initialize
        chatInput.focus();

        // Functions
        function sendMessage() {
            const message = chatInput.value.trim();
            if (!message) return;

            // Add user message to UI
            addMessageToUI('user', message);
            
            // Add to chat history
            chatHistory.push({
                role: 'user',
                content: message
            });

            // Clear input
            chatInput.value = '';
            
            // Show typing indicator
            typingIndicator.style.display = 'block';
            chatMessages.scrollTop = chatMessages.scrollHeight;
            
            // Disable input while waiting for response
            chatInput.disabled = true;
            sendButton.disabled = true;

            // Send to API
            fetchAIResponse(chatHistory);
        }

        function addMessageToUI(role, content) {
            const messageDiv = document.createElement('div');
            messageDiv.classList.add('message');
            messageDiv.classList.add(role === 'user' ? 'user-message' : 'bot-message');
            
            // Process content for markdown-like formatting (basic)
            content = processContent(content);
            
            messageDiv.innerHTML = content;
            
            // Add timestamp
            const timeDiv = document.createElement('div');
            timeDiv.classList.add('message-time');
            timeDiv.textContent = 'Just now';
            messageDiv.appendChild(timeDiv);
            
            // Insert before typing indicator
            chatMessages.insertBefore(messageDiv, typingIndicator);
            chatMessages.scrollTop = chatMessages.scrollHeight;
        }

        function processContent(content) {
            // Enhanced markdown processing
            
            // Code blocks with language specification
            content = content.replace(/```(\w+)?\n([\s\S]*?)```/g, function(match, language, code) {
                language = language || '';
                return `<pre class="language-${language}"><code class="language-${language}">${code}</code></pre>`;
            });
            
            // Inline code
            content = content.replace(/`([^`]+)`/g, '<code>$1</code>');
            
            // Headers
            content = content.replace(/^# (.*?)$/gm, '<h1>$1</h1>');
            content = content.replace(/^## (.*?)$/gm, '<h2>$1</h2>');
            content = content.replace(/^### (.*?)$/gm, '<h3>$1</h3>');
            content = content.replace(/^#### (.*?)$/gm, '<h4>$1</h4>');
            content = content.replace(/^##### (.*?)$/gm, '<h5>$1</h5>');
            content = content.replace(/^###### (.*?)$/gm, '<h6>$1</h6>');
            
            // Bold
            content = content.replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>');
            
            // Italic
            content = content.replace(/\*(.*?)\*/g, '<em>$1</em>');
            
            // Lists
            content = content.replace(/^\s*[\*\-]\s+(.*?)$/gm, '<li>$1</li>');
            content = content.replace(/(<li>.*?<\/li>)(\s*<li>)/g, '$1<li>');
            content = content.replace(/(<li>.*?<\/li>)(?!\s*<li>)/g, '<ul>$1</ul>');
            
            // Numbered lists
            content = content.replace(/^\s*\d+\.\s+(.*?)$/gm, '<li>$1</li>');
            content = content.replace(/(<li>.*?<\/li>)(\s*<li>)/g, '$1<li>');
            content = content.replace(/(<li>.*?<\/li>)(?!\s*<li>)/g, '<ol>$1</ol>');
            
            // Links
            content = content.replace(/\[(.*?)\]\((.*?)\)/g, '<a href="$2" target="_blank">$1</a>');
            
            // Line breaks
            content = content.replace(/\n\n/g, '<br><br>');
            content = content.replace(/\n/g, '<br>');
            
            return content;
        }

        async function fetchAIResponse(history) {
            try {
                const response = await fetch(API_URL, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${API_KEY}`
                    },
                    body: JSON.stringify({
                        model: MODEL,
                        messages: history
                    })
                });

                const data = await response.json();
                
                if (data.error) {
                    throw new Error(data.error.message || 'Error communicating with AI');
                }

                // Hide typing indicator
                typingIndicator.style.display = 'none';
                
                // Get AI response
                const aiMessage = data.choices[0].message.content;
                
                // Add to UI
                addMessageToUI('bot', aiMessage);
                
                // Add to chat history
                chatHistory.push({
                    role: 'assistant',
                    content: aiMessage
                });
                
                // Re-enable input
                chatInput.disabled = false;
                sendButton.disabled = false;
                chatInput.focus();
                
            } catch (error) {
                console.error('Error:', error);
                
                // Hide typing indicator
                typingIndicator.style.display = 'none';
                
                // Show error message
                addMessageToUI('bot', `Sorry, there was an error: ${error.message}`);
                
                // Re-enable input
                chatInput.disabled = false;
                sendButton.disabled = false;
                chatInput.focus();
            }
        }
    </script>
</body>
</html>