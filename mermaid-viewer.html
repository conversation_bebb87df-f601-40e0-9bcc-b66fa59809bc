<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="utf-8" />
  <title>Mermaid Live Editor Example</title>
  <style>
    body { font-family: sans-serif; margin: 2rem; }
    textarea { width: 100%; height: 200px; }
    #diagram { border: 1px solid #ddd; padding: 1rem; margin-bottom: 1rem; }
    button { margin-right: 10px; }
  </style>
</head>
<body>

  <h2>Edit Mermaid Diagram:</h2>
  <textarea id="code">
graph LR
  A[Start] --> B{Is it working?}
  B -->|Yes| C[Great!]
  B -->|No| D[Fix it]
  D --> B
  </textarea>
  <br />
  <button id="renderBtn">Render Diagram</button>
  <button id="downloadBtn">Download SVG</button>

  <h2>Diagram Preview:</h2>
  <div id="diagram"></div>

  <script type="module">
    import mermaid from 'https://cdn.jsdelivr.net/npm/mermaid@11/dist/mermaid.esm.min.mjs';

    let currentSvg = ''; // Store the latest SVG content

    mermaid.initialize({ startOnLoad: false });

    async function renderDiagram() {
      const code = document.getElementById('code').value;
      const placeholderId = 'renderedGraph';
      try {
        const { svg, bindFunctions } = await mermaid.render(placeholderId, code);
        const container = document.getElementById('diagram');
        container.innerHTML = svg;
        currentSvg = svg; // Save the SVG for download
        if (bindFunctions) bindFunctions(container);
      } catch (err) {
        document.getElementById('diagram').textContent = '❌ Error: ' + err.str;
        console.error(err);
      }
    }

    function downloadSvg() {
      if (!currentSvg) {
        alert("Please render a diagram first.");
        return;
      }

      const blob = new Blob([currentSvg], { type: 'image/svg+xml' });
      const url = URL.createObjectURL(blob);

      const link = document.createElement('a');
      link.href = url;
      link.download = 'diagram.svg';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);
    }

    document.getElementById('renderBtn').addEventListener('click', renderDiagram);
    document.getElementById('downloadBtn').addEventListener('click', downloadSvg);

    // Initial render
    renderDiagram();
  </script>

</body>
</html>