<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PAIP - PDF to Image Converter</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

        :root {
            --primary-green: #2e7d32;
            --light-green: #4caf50;
            --accent-green: #66bb6a;
            --secondary-blue: #1976d2;
            --accent-orange: #ff9800;
            --white: #ffffff;
            --light-gray: #f5f5f5;
            --medium-gray: #e0e0e0;
            --dark-gray: #333333;
            --text-gray: #666666;
            --shadow-light: 0 2px 8px rgba(0,0,0,0.1);
            --shadow-medium: 0 4px 12px rgba(0,0,0,0.15);
            --border-radius: 8px;
            --border-radius-small: 6px;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: var(--light-gray);
            min-height: 100vh;
            padding: 20px;
            margin: 0;
        }

        /* Navigation Styles */
        .navbar {
            background: var(--white);
            box-shadow: var(--shadow-light);
            border-bottom: 1px solid var(--medium-gray);
            position: sticky;
            top: 0;
            z-index: 1000;
            margin: -20px -20px 20px -20px;
        }

        .nav-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            height: 70px;
        }

        .nav-brand {
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--primary-green);
            text-decoration: none;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .nav-brand:hover {
            color: var(--light-green);
        }

        .nav-menu {
            display: flex;
            list-style: none;
            gap: 0;
            margin: 0;
            padding: 0;
        }

        .nav-item {
            position: relative;
        }

        .nav-link {
            display: block;
            padding: 15px 20px;
            color: var(--dark-gray);
            text-decoration: none;
            font-weight: 500;
            transition: all 0.3s ease;
            border-radius: var(--border-radius-small);
            margin: 0 5px;
        }

        .nav-link:hover {
            color: var(--primary-green);
            background: rgba(46, 125, 50, 0.1);
        }

        .nav-link.active {
            color: var(--primary-green);
            background: rgba(46, 125, 50, 0.15);
            font-weight: 600;
        }

        .hamburger {
            display: none;
            flex-direction: column;
            cursor: pointer;
            padding: 5px;
            border-radius: var(--border-radius-small);
            transition: background 0.3s ease;
        }

        .hamburger:hover {
            background: rgba(46, 125, 50, 0.1);
        }

        .hamburger span {
            width: 25px;
            height: 3px;
            background: var(--primary-green);
            margin: 3px 0;
            transition: 0.3s;
            border-radius: 2px;
        }

        .hamburger.active span:nth-child(1) {
            transform: rotate(-45deg) translate(-5px, 6px);
        }

        .hamburger.active span:nth-child(2) {
            opacity: 0;
        }

        .hamburger.active span:nth-child(3) {
            transform: rotate(45deg) translate(-5px, -6px);
        }

        /* Mobile Styles */
        @media (max-width: 768px) {
            .nav-menu {
                position: fixed;
                left: -100%;
                top: 70px;
                flex-direction: column;
                background: var(--white);
                width: 100%;
                text-align: center;
                transition: 0.3s;
                box-shadow: var(--shadow-medium);
                border-top: 1px solid var(--medium-gray);
                padding: 20px 0;
            }

            .nav-menu.active {
                left: 0;
            }

            .nav-item {
                margin: 5px 0;
            }

            .nav-link {
                padding: 15px 20px;
                margin: 0 20px;
                border-radius: var(--border-radius);
            }

            .hamburger {
                display: flex;
            }
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: var(--white);
            border-radius: var(--border-radius);
            padding: 40px;
            box-shadow: var(--shadow-light);
            border: 1px solid var(--medium-gray);
        }

        h1 {
            color: var(--primary-green);
            text-align: center;
            font-size: 2.5rem;
            font-weight: 600;
            margin-bottom: 10px;
        }

        .subtitle {
            text-align: center;
            color: var(--text-gray);
            font-size: 1.1rem;
            margin-bottom: 40px;
        }

        .section-card {
            background: var(--white);
            border-radius: var(--border-radius);
            padding: 25px;
            margin-bottom: 25px;
            box-shadow: var(--shadow-light);
            border: 1px solid var(--medium-gray);
            transition: box-shadow 0.2s ease;
        }

        .section-card:hover {
            box-shadow: var(--shadow-medium);
        }

        .upload-section {
            border: 2px dashed var(--light-green);
            text-align: center;
            transition: border-color 0.2s ease, background-color 0.2s ease;
        }

        .upload-section:hover {
            border-color: var(--primary-green);
            background-color: rgba(76, 175, 80, 0.05);
        }

        .upload-section.dragover {
            border-color: var(--primary-green);
            background-color: rgba(76, 175, 80, 0.1);
        }

        .section-title {
            font-size: 1.4rem;
            font-weight: 600;
            margin-bottom: 15px;
            color: var(--primary-green);
        }

        .btn {
            border: none;
            border-radius: var(--border-radius-small);
            cursor: pointer;
            font-family: inherit;
            font-weight: 500;
            transition: all 0.2s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
        }

        .btn:hover {
            transform: translateY(-1px);
        }

        .btn:active {
            transform: translateY(0);
        }

        .upload-btn {
            background: var(--light-green);
            color: white;
            padding: 12px 24px;
            font-size: 16px;
            margin: 10px;
            box-shadow: var(--shadow-light);
        }

        .upload-btn:hover {
            background: var(--primary-green);
            box-shadow: var(--shadow-medium);
        }

        .process-btn {
            background: var(--primary-green);
            color: white;
            padding: 16px 32px;
            font-size: 18px;
            width: 100%;
            margin-top: 25px;
            box-shadow: var(--shadow-light);
        }

        .process-btn:hover {
            background: var(--light-green);
            box-shadow: var(--shadow-medium);
        }

        .process-btn:disabled {
            background: var(--medium-gray);
            color: var(--text-gray);
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }

        .copy-btn {
            background: var(--accent-orange);
            color: white;
            padding: 12px 24px;
            font-size: 14px;
            margin: 15px 0;
            min-width: 200px;
            box-shadow: var(--shadow-light);
            transition: all 0.4s ease;
        }

        .copy-btn:hover {
            background: #f57c00;
            box-shadow: var(--shadow-medium);
        }

        .copy-btn.copied {
            background: var(--primary-green);
            transition: all 60s ease;
        }

        .copy-btn.copied:hover {
            background: var(--primary-green);
        }

        .copy-btn .checkmark {
            display: none;
        }

        .copy-btn.copied .checkmark {
            display: inline;
        }

        .copy-btn.copied .copy-text {
            display: none;
        }

        .copy-btn .copied-text {
            display: none;
        }

        .copy-btn.copied .copied-text {
            display: inline;
        }

        .download-btn {
            background: var(--secondary-blue);
            color: white;
            padding: 12px 24px;
            font-size: 14px;
            min-width: 200px;
            box-shadow: var(--shadow-light);
            transition: all 0.3s ease;
        }

        .download-btn:hover {
            background: #1565c0;
            box-shadow: var(--shadow-medium);
        }

        .reset-btn {
            background: var(--text-gray);
            color: white;
            padding: 10px 20px;
            font-size: 14px;
            margin: 10px;
            box-shadow: var(--shadow-light);
        }

        .reset-btn:hover {
            background: var(--dark-gray);
            box-shadow: var(--shadow-medium);
        }

        .file-list {
            margin-top: 25px;
            display: none;
        }

        .file-item {
            background: var(--white);
            border: 1px solid var(--medium-gray);
            border-radius: var(--border-radius-small);
            padding: 20px;
            margin-bottom: 15px;
            transition: box-shadow 0.2s ease;
        }

        .file-item:hover {
            box-shadow: var(--shadow-medium);
        }

        .file-name {
            font-weight: 600;
            font-size: 16px;
            margin-bottom: 8px;
            color: var(--dark-gray);
        }

        .file-status {
            font-size: 14px;
            font-weight: 500;
            padding: 4px 12px;
            border-radius: 15px;
            display: inline-block;
            margin-bottom: 10px;
        }

        .file-status.processing {
            background: var(--accent-orange);
            color: white;
        }

        .file-status.completed {
            background: var(--primary-green);
            color: white;
        }

        .file-status.error {
            background: #f44336;
            color: white;
        }

        .progress-bar {
            width: 100%;
            height: 6px;
            background: var(--medium-gray);
            border-radius: 3px;
            overflow: hidden;
            margin: 12px 0;
        }

        .progress-fill {
            height: 100%;
            background: var(--light-green);
            width: 0%;
            transition: width 0.3s ease;
            border-radius: 3px;
        }

        .images-container {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }

        .image-item {
            background: var(--white);
            border: 1px solid var(--medium-gray);
            border-radius: var(--border-radius-small);
            padding: 15px;
            text-align: center;
            transition: box-shadow 0.2s ease;
        }

        .image-item:hover {
            box-shadow: var(--shadow-medium);
        }

        .image-item {
            animation: slideInUp 0.3s ease-out;
        }

        @keyframes slideInUp {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .image-preview {
            max-width: 100%;
            max-height: 400px;
            border: 1px solid var(--medium-gray);
            border-radius: var(--border-radius-small);
            margin-bottom: 15px;
            cursor: pointer;
        }

        .image-info {
            font-size: 14px;
            color: var(--text-gray);
            margin-bottom: 15px;
        }

        .image-controls {
            display: flex;
            gap: 10px;
            justify-content: center;
            flex-wrap: wrap;
        }

        .image-btn {
            background: var(--light-green);
            color: white;
            padding: 8px 16px;
            font-size: 12px;
            border: none;
            border-radius: var(--border-radius-small);
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .image-btn:hover {
            background: var(--primary-green);
            transform: translateY(-1px);
        }

        .image-btn.copy {
            background: var(--accent-orange);
        }

        .image-btn.copy:hover {
            background: #f57c00;
        }

        .image-btn.download {
            background: var(--secondary-blue);
        }

        .image-btn.download:hover {
            background: #1565c0;
        }

        .markdown-output {
            background: var(--light-gray);
            border: 1px solid var(--medium-gray);
            border-radius: var(--border-radius-small);
            padding: 20px;
            max-height: 600px;
            overflow-y: auto;
            white-space: pre-wrap;
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            line-height: 1.5;
            font-size: 13px;
        }

        .markdown-output::-webkit-scrollbar {
            width: 8px;
        }

        .markdown-output::-webkit-scrollbar-track {
            background: var(--medium-gray);
            border-radius: 4px;
        }

        .markdown-output::-webkit-scrollbar-thumb {
            background: var(--light-green);
            border-radius: 4px;
        }

        .processing-indicator {
            background: var(--light-gray);
            border: 2px dashed var(--light-green);
            border-radius: var(--border-radius-small);
            padding: 20px;
            text-align: center;
            margin-bottom: 20px;
            color: var(--text-gray);
        }

        .processing-indicator .spinner-large {
            width: 40px;
            height: 40px;
            border: 4px solid var(--medium-gray);
            border-top: 4px solid var(--light-green);
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 15px;
        }

        .results-section {
            margin-top: 30px;
            display: none;
        }

        .results-controls {
            display: flex;
            gap: 15px;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }

        .notification {
            padding: 15px 20px;
            border-radius: var(--border-radius-small);
            margin: 15px 0;
            border-left: 4px solid;
        }

        .notification.success {
            background: rgba(76, 175, 80, 0.1);
            color: var(--primary-green);
            border-left-color: var(--primary-green);
        }

        .notification.error {
            background: rgba(244, 67, 54, 0.1);
            color: #d32f2f;
            border-left-color: #f44336;
        }

        .notification.warning {
            background: rgba(255, 152, 0, 0.1);
            color: #f57c00;
            border-left-color: #ff9800;
        }

        .notification.info {
            background: rgba(33, 150, 243, 0.1);
            color: var(--secondary-blue);
            border-left-color: var(--secondary-blue);
        }

        .spinner {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            border-top-color: #fff;
            animation: spin 1s ease-in-out infinite;
            margin-left: 10px;
            vertical-align: middle;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }

        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.7);
            display: none;
            justify-content: center;
            align-items: center;
            z-index: 1000;
        }

        .loading-content {
            background: var(--white);
            padding: 40px;
            border-radius: var(--border-radius);
            text-align: center;
            max-width: 400px;
            box-shadow: var(--shadow-medium);
        }

        .loading-spinner {
            width: 50px;
            height: 50px;
            border: 4px solid var(--medium-gray);
            border-top: 4px solid var(--primary-green);
            border-radius: 50%;
            animation: loadingSpin 1s linear infinite;
            margin: 0 auto 20px;
        }

        @keyframes loadingSpin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .loading-title {
            font-size: 1.4rem;
            font-weight: 600;
            margin-bottom: 10px;
            color: var(--primary-green);
        }

        .loading-message {
            color: var(--text-gray);
            line-height: 1.5;
        }

        /* Footer Styling */
        .footer {
            background: linear-gradient(135deg, var(--primary-green) 0%, var(--light-green) 100%);
            color: white;
            text-align: center;
            padding: 30px 20px;
            margin-top: 50px;
            border-radius: var(--border-radius) var(--border-radius) 0 0;
            position: relative;
            overflow: hidden;
        }

        .footer::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 1px;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
        }

        .footer-content {
            max-width: 1200px;
            margin: 0 auto;
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 15px;
        }

        .footer-brand {
            font-size: 1.2rem;
            font-weight: 600;
            margin-bottom: 5px;
        }

        .footer-tagline {
            font-size: 0.9rem;
            opacity: 0.9;
            margin-bottom: 10px;
        }

        .footer-links {
            display: flex;
            gap: 20px;
            margin-bottom: 15px;
        }

        .footer-link {
            color: white;
            text-decoration: none;
            font-size: 0.9rem;
            opacity: 0.8;
            transition: opacity 0.2s ease;
        }

        .footer-link:hover {
            opacity: 1;
            text-decoration: underline;
        }

        .footer-copyright {
            font-size: 0.85rem;
            opacity: 0.7;
            border-top: 1px solid rgba(255,255,255,0.2);
            padding-top: 15px;
            width: 100%;
        }

        .footer-powered {
            font-size: 0.8rem;
            opacity: 0.6;
            font-style: italic;
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .container {
                padding: 20px;
                margin: 10px;
            }

            h1 {
                font-size: 2rem;
            }

            .section-card {
                padding: 20px;
            }

            .btn {
                padding: 12px 20px;
                font-size: 14px;
            }

            .footer {
                margin-top: 30px;
                padding: 25px 15px;
            }

            .footer-links {
                flex-direction: column;
                gap: 10px;
            }

            .footer-brand {
                font-size: 1.1rem;
            }
        }
    </style>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.11.174/pdf.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jszip/3.10.1/jszip.min.js"></script>
</head>
<body>
    <!-- Navigation Menu -->
    <nav class="navbar">
        <div class="nav-container">
            <a href="index.html" class="nav-brand">
                🎯 PAIP
            </a>
            <ul class="nav-menu" id="nav-menu">
                <li class="nav-item">
                    <a href="index.html" class="nav-link">🏠 Home</a>
                </li>
                <li class="nav-item">
                    <a href="gemini_file_upload_v3.html" class="nav-link">📄 Document Analysis</a>
                </li>
                <li class="nav-item">
                    <a href="gemini_format_post.html" class="nav-link">📊 PDF to CSV</a>
                </li>
                <li class="nav-item">
                    <a href="paip_qc.html" class="nav-link">🔍 Quality Check</a>
                </li>
                <li class="nav-item">
                    <a href="html_markdownviewer.html" class="nav-link">📝 Markdown Viewer</a>
                </li>
                <li class="nav-item">
                    <a href="pdf_to_markdown.html" class="nav-link active">🖼️ PDF to Image</a>
                </li>
            </ul>
            <div class="hamburger" id="hamburger">
                <span></span>
                <span></span>
                <span></span>
            </div>
        </div>
    </nav>

    <div class="container">
        <h1>PDF to Image Converter</h1>
        <p class="subtitle">
            🖼️ Convert PDF documents to high-quality images<br>
            📥 Extract each page as a separate image file
        </p>

        <!-- Upload Section -->
        <div class="section-card upload-section" id="uploadSection">
            <h3 class="section-title">📤 Upload PDF Files</h3>
            <div style="font-size: 48px; margin-bottom: 20px; opacity: 0.6;">📄</div>
            <p style="color: var(--dark-gray); margin-bottom: 20px;">
                Drag and drop PDF files here or click to select multiple files
            </p>
            <input type="file" id="fileInput" accept=".pdf" multiple style="display: none;" />
            <button class="btn upload-btn" onclick="document.getElementById('fileInput').click()">
                <span>📁</span>
                Choose PDF Files
            </button>
        </div>

        <!-- File List Section -->
        <div class="section-card file-list" id="fileList">
            <h3 class="section-title">📋 Uploaded Files</h3>
            <div id="fileItems"></div>
        </div>

        <!-- Process Button -->
        <button class="btn process-btn" id="processBtn" onclick="processAllFiles()" disabled>
            <span>�️</span>
            <span id="processButtonText">Upload PDF files first</span>
            <span id="processSpinner" class="spinner" style="display: none;"></span>
        </button>

        <!-- Reset Button -->
        <button class="btn reset-btn" onclick="resetApplication()">
            <span>🔄</span>
            Reset All
        </button>

        <!-- Results Section -->
        <div class="section-card results-section" id="resultsSection">
            <h3 class="section-title">🖼️ Generated Images</h3>
            <div class="results-controls">
                <button class="btn download-btn" id="downloadAllBtn" onclick="downloadAllImages()">
                    <span>📦</span>
                    Download All Images
                </button>
            </div>
            <div class="images-container" id="imagesContainer"></div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="footer">
        <div class="footer-content">
            <div class="footer-brand">PAIP - Positions AI Processing</div>
            <div class="footer-tagline">Intelligent PDF Processing for Document Analysis</div>

            <div class="footer-links">
                <a href="#" class="footer-link">About</a>
                <a href="#" class="footer-link">Documentation</a>
                <a href="#" class="footer-link">Support</a>
                <a href="#" class="footer-link">Privacy Policy</a>
            </div>

            <div class="footer-copyright">
                <div>© 2025 Dakoii Systems. All rights reserved.</div>
                <div class="footer-powered">Powered by Dakoii AI</div>
            </div>
        </div>
    </footer>

    <!-- Loading Overlay -->
    <div class="loading-overlay" id="loadingOverlay">
        <div class="loading-content">
            <div class="loading-spinner"></div>
            <h3 class="loading-title" id="loadingTitle">🖼️ Converting to Images...</h3>
            <p class="loading-message" id="loadingMessage">Please wait while we convert your PDF pages to high-quality images.</p>
        </div>
    </div>

    <!-- Notification Container -->
    <div id="notificationContainer"></div>

    <script>
        // ===== PDF TO MARKDOWN CONVERTER LIBRARY =====

        // ===== CONFIGURATION =====
        class PDFToImageConverter {
            constructor() {
                this.uploadedFiles = new Map();
                this.extractedImages = new Map();
                this.fileStatuses = new Map();

                // Set up PDF.js worker
                pdfjsLib.GlobalWorkerOptions.workerSrc = 'https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.11.174/pdf.worker.min.js';

                this.initializeEventListeners();
            }

            initializeEventListeners() {
                const uploadSection = document.getElementById('uploadSection');
                const fileInput = document.getElementById('fileInput');

                // Drag and drop events
                uploadSection.addEventListener('dragover', this.handleDragOver.bind(this));
                uploadSection.addEventListener('dragleave', this.handleDragLeave.bind(this));
                uploadSection.addEventListener('drop', this.handleDrop.bind(this));

                // File input change
                fileInput.addEventListener('change', this.handleFileInputChange.bind(this));

                // Navigation hamburger
                this.initializeNavigation();
            }

            initializeNavigation() {
                const hamburger = document.getElementById('hamburger');
                const navMenu = document.getElementById('nav-menu');

                if (hamburger && navMenu) {
                    hamburger.addEventListener('click', () => {
                        hamburger.classList.toggle('active');
                        navMenu.classList.toggle('active');
                    });
                }
            }

            handleDragOver(e) {
                e.preventDefault();
                document.getElementById('uploadSection').classList.add('dragover');
            }

            handleDragLeave(e) {
                e.preventDefault();
                document.getElementById('uploadSection').classList.remove('dragover');
            }

            handleDrop(e) {
                e.preventDefault();
                document.getElementById('uploadSection').classList.remove('dragover');

                const files = Array.from(e.dataTransfer.files).filter(file => file.type === 'application/pdf');
                if (files.length > 0) {
                    this.handleFileUpload(files);
                } else {
                    this.showNotification('Please select valid PDF files only.', 'error');
                }
            }

            handleFileInputChange(e) {
                const files = Array.from(e.target.files).filter(file => file.type === 'application/pdf');
                if (files.length > 0) {
                    this.handleFileUpload(files);
                }
            }

            handleFileUpload(files) {
                this.showNotification(`Processing ${files.length} PDF file(s)...`, 'info');

                // Show file list section
                document.getElementById('fileList').style.display = 'block';

                files.forEach(file => {
                    // Store file
                    this.uploadedFiles.set(file.name, file);
                    this.updateFileStatus(file.name, 'Uploaded - Ready for conversion');

                    // Create UI element for file
                    this.createFileItem(file);

                    // Start image conversion
                    this.convertPDFToImages(file, (progress) => this.updateProgressBar(file.name, progress));
                });
            }

            createFileItem(file) {
                const fileItems = document.getElementById('fileItems');

                const fileItem = document.createElement('div');
                fileItem.className = 'file-item';
                fileItem.setAttribute('data-filename', file.name);

                fileItem.innerHTML = `
                    <div class="file-name">${file.name}</div>
                    <div class="file-status">Uploaded - Ready for conversion</div>
                    <div class="progress-bar">
                        <div class="progress-fill" id="progress-${file.name.replace(/[^a-zA-Z0-9]/g, '_')}"></div>
                    </div>
                `;

                fileItems.appendChild(fileItem);
            }

            async convertPDFToImages(file, progressCallback) {
                const filename = file.name;
                this.updateFileStatus(filename, 'Converting PDF to images...');

                // Add processing indicator
                this.addProcessingIndicator(filename);

                try {
                    const arrayBuffer = await file.arrayBuffer();

                    // Configure PDF.js with better settings
                    const loadingTask = pdfjsLib.getDocument({
                        data: arrayBuffer,
                        cMapUrl: 'https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.11.174/cmaps/',
                        cMapPacked: true,
                        enableXfa: false,
                        isEvalSupported: false,
                        disableFontFace: false,
                        useSystemFonts: true
                    });

                    const pdf = await loadingTask.promise;
                    const totalPages = pdf.numPages;

                    console.log(`PDF loaded successfully: ${filename}, ${totalPages} pages`);

                    let extractedImages = [];

                    for (let pageNum = 1; pageNum <= totalPages; pageNum++) {
                        try {
                            const page = await pdf.getPage(pageNum);

                            // Convert page to HD image
                            const imageData = await this.convertPageToHDImage(page, pageNum, filename);
                            if (imageData) {
                                extractedImages.push(imageData);
                            }

                            // Update progress
                            const progress = (pageNum / totalPages) * 100;
                            progressCallback(progress);

                        } catch (pageError) {
                            console.error(`Error processing page ${pageNum}:`, pageError);
                            // Add placeholder for failed image conversion
                            extractedImages.push(null);
                        }
                    }

                    // Store extracted images
                    this.extractedImages.set(filename, extractedImages);
                    this.updateFileStatus(filename, 'Image conversion completed');

                    // Remove processing indicator
                    this.removeProcessingIndicator(filename);

                    // Update process button state
                    this.updateProcessButtonState();

                } catch (error) {
                    this.handleConversionError(filename, error);
                }
            }

            async convertPageToHDImage(page, pageNum, filename) {
                try {
                    // Set scale for HD quality (2.0 for better performance while maintaining quality)
                    const scale = 2.0;
                    const viewport = page.getViewport({ scale: scale });

                    // Create canvas
                    const canvas = document.createElement('canvas');
                    const context = canvas.getContext('2d');

                    // Set canvas dimensions
                    canvas.height = viewport.height;
                    canvas.width = viewport.width;

                    // Set white background
                    context.fillStyle = 'white';
                    context.fillRect(0, 0, canvas.width, canvas.height);

                    // Render page to canvas with proper configuration
                    const renderContext = {
                        canvasContext: context,
                        viewport: viewport,
                        enableWebGL: false,
                        renderInteractiveForms: false
                    };

                    console.log(`Converting page ${pageNum} of ${filename}...`);

                    // Wait for the page to render completely
                    const renderTask = page.render(renderContext);
                    await renderTask.promise;

                    // Ensure rendering is complete
                    await new Promise(resolve => setTimeout(resolve, 200));

                    // Check if canvas has any content, if not try alternative rendering
                    const initialImageData = context.getImageData(0, 0, canvas.width, canvas.height);
                    const initialPixels = initialImageData.data;
                    let hasInitialContent = false;

                    for (let i = 0; i < initialPixels.length; i += 4) {
                        const r = initialPixels[i];
                        const g = initialPixels[i + 1];
                        const b = initialPixels[i + 2];
                        if (r !== 255 || g !== 255 || b !== 255) {
                            hasInitialContent = true;
                            break;
                        }
                    }

                    // If no content, try alternative rendering approach
                    if (!hasInitialContent) {
                        console.log(`Attempting alternative rendering for page ${pageNum}...`);

                        // Clear canvas and try with different settings
                        context.clearRect(0, 0, canvas.width, canvas.height);
                        context.fillStyle = 'white';
                        context.fillRect(0, 0, canvas.width, canvas.height);

                        // Try with different render context settings
                        const altRenderContext = {
                            canvasContext: context,
                            viewport: viewport,
                            enableWebGL: false,
                            renderInteractiveForms: true,
                            intent: 'display'
                        };

                        const altRenderTask = page.render(altRenderContext);
                        await altRenderTask.promise;
                        await new Promise(resolve => setTimeout(resolve, 300));
                    }

                    // Convert canvas to high-quality image data URL
                    const imageDataUrl = canvas.toDataURL('image/png', 1.0);
                    const base64Data = imageDataUrl.split(',')[1];

                    // Verify the image has content (not just white)
                    const imageData = context.getImageData(0, 0, canvas.width, canvas.height);
                    const pixels = imageData.data;
                    let hasContent = false;

                    // Check if there's any non-white content
                    for (let i = 0; i < pixels.length; i += 4) {
                        const r = pixels[i];
                        const g = pixels[i + 1];
                        const b = pixels[i + 2];
                        if (r !== 255 || g !== 255 || b !== 255) {
                            hasContent = true;
                            break;
                        }
                    }

                    if (!hasContent) {
                        console.warn(`Page ${pageNum} appears to be blank or failed to render properly`);
                    }

                    console.log(`✓ Converted page ${pageNum} to HD image (${canvas.width}x${canvas.height}) - Has content: ${hasContent}`);

                    return {
                        pageNumber: pageNum,
                        filename: filename,
                        dataUrl: imageDataUrl,
                        base64Data: base64Data,
                        mimeType: 'image/png',
                        width: canvas.width,
                        height: canvas.height,
                        fileSize: Math.round(base64Data.length * 0.75), // Approximate file size in bytes
                        hasContent: hasContent
                    };

                } catch (error) {
                    console.error(`Error converting page ${pageNum} to image:`, error);
                    return null;
                }
            }

            // Image Display Functions

            displayImages(filename) {
                const images = this.extractedImages.get(filename);
                if (images && images.length > 0) {
                    // Display all images in the container
                    this.displayImageGrid(images);
                    // Show results section
                    document.getElementById('resultsSection').style.display = 'block';
                }
            }

            displayImageGrid(images) {
                const container = document.getElementById('imagesContainer');
                container.innerHTML = '';

                images.forEach((imageData, index) => {
                    if (imageData && imageData.dataUrl) {
                        const imageItem = document.createElement('div');
                        imageItem.className = 'image-item';
                        
                        imageItem.innerHTML = `
                            <img src="${imageData.dataUrl}" alt="Page ${imageData.pageNumber}" class="image-preview" onclick="openImageModal('${imageData.dataUrl}', ${imageData.pageNumber})">
                            <div class="image-info">
                                Page ${imageData.pageNumber} - ${imageData.width}x${imageData.height}px
                            </div>
                            <div class="image-controls">
                                <button class="image-btn download" onclick="downloadImage('${imageData.dataUrl}', 'page-${imageData.pageNumber}', '${imageData.filename}')">
                                    📥 Download
                                </button>
                                <button class="image-btn copy" onclick="copyImageToClipboard('${imageData.dataUrl}', ${imageData.pageNumber})">
                                    📋 Copy
                                </button>
                            </div>
                        `;
                        
                        container.appendChild(imageItem);
                    }
                });
            }

            openImageModal(dataUrl, pageNumber) {
                // Create modal for full-size image viewing
                const modal = document.createElement('div');
                modal.style.cssText = `
                    position: fixed;
                    top: 0;
                    left: 0;
                    width: 100%;
                    height: 100%;
                    background: rgba(0,0,0,0.9);
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    z-index: 2000;
                    cursor: pointer;
                `;
                
                modal.innerHTML = `
                    <div style="max-width: 95%; max-height: 95%; position: relative;">
                        <img src="${dataUrl}" style="max-width: 100%; max-height: 100%; border: 1px solid #ccc;">
                        <div style="position: absolute; top: 10px; right: 10px; background: rgba(0,0,0,0.7); color: white; padding: 5px 10px; border-radius: 3px;">
                            Page ${pageNumber}
                        </div>
                    </div>
                `;
                
                modal.onclick = () => document.body.removeChild(modal);
                document.body.appendChild(modal);
            }

            addProcessingIndicator(filename) {
                // Show processing status in the images container
                const imagesContainer = document.getElementById('imagesContainer');
                const resultsSection = document.getElementById('resultsSection');

                // Show results section
                resultsSection.style.display = 'block';

                // Add processing message
                imagesContainer.innerHTML = `
                    <div style="text-align: center; padding: 40px; color: #666; font-size: 16px;">
                        🖼️ Converting ${filename} to images...<br><br>
                        <div class="spinner-large" style="width: 40px; height: 40px; border: 4px solid #e0e0e0; border-top: 4px solid #4caf50; border-radius: 50%; animation: spin 1s linear infinite; margin: 0 auto 20px;"></div>
                        Please wait while we convert each PDF page to a high-quality image.
                    </div>
                `;

                console.log(`Processing indicator added for ${filename}`);
            }

            removeProcessingIndicator(filename) {
                // Display the images
                this.displayImages(filename);
                console.log(`Processing indicator removed for ${filename}`);
            }

            updateFileStatus(filename, status) {
                this.fileStatuses.set(filename, status);

                // Update UI
                const fileItem = document.querySelector(`[data-filename="${filename}"]`);
                if (fileItem) {
                    const statusElement = fileItem.querySelector('.file-status');
                    statusElement.textContent = status;
                    statusElement.className = `file-status ${this.getStatusClass(status)}`;
                }

                // Check if all files are ready for processing
                this.updateProcessButtonState();
            }

            getStatusClass(status) {
                if (status.includes('Error') || status.includes('Failed')) return 'error';
                if (status.includes('completed')) return 'completed';
                if (status.includes('processing') || status.includes('Extracting') || status.includes('Converting')) return 'processing';
                return '';
            }

            updateProgressBar(fileId, progress) {
                const progressElement = document.getElementById(`progress-${fileId.replace(/[^a-zA-Z0-9]/g, '_')}`);
                if (progressElement) {
                    progressElement.style.width = `${progress}%`;
                }
            }

            updateProcessButtonState() {
                const processBtn = document.getElementById('processBtn');
                const processButtonText = document.getElementById('processButtonText');

                const allCompleted = Array.from(this.fileStatuses.values()).every(status =>
                    status.includes('completed')
                );

                if (this.uploadedFiles.size === 0) {
                    processBtn.disabled = true;
                    processButtonText.textContent = 'Upload PDF files first';
                } else if (allCompleted && this.extractedImages.size > 0) {
                    processBtn.disabled = false;
                    processButtonText.textContent = 'View All Images';
                } else if (this.uploadedFiles.size > 0 && this.extractedImages.size === 0) {
                    processBtn.disabled = true;
                    processButtonText.textContent = 'Converting to images...';
                } else {
                    processBtn.disabled = true;
                    processButtonText.textContent = 'Processing files...';
                }
            }

            handleConversionError(filename, error) {
                console.error(`Error converting ${filename}:`, error);
                this.updateFileStatus(filename, 'Error converting file');
                this.showNotification(`Error converting ${filename}: ${error.message}`, 'error');
            }

            showNotification(message, type = 'info') {
                const container = document.getElementById('notificationContainer');
                const notification = document.createElement('div');
                notification.className = `notification ${type}`;
                notification.textContent = message;

                container.appendChild(notification);

                // Auto remove after 5 seconds
                setTimeout(() => {
                    if (notification.parentNode) {
                        notification.parentNode.removeChild(notification);
                    }
                }, 5000);
            }

            showLoadingState() {
                document.getElementById('loadingOverlay').style.display = 'flex';
            }

            hideLoadingState() {
                document.getElementById('loadingOverlay').style.display = 'none';
            }

            async processAllFiles() {
                if (this.extractedImages.size === 0) {
                    this.showNotification('No files have been converted yet.', 'error');
                    return;
                }

                try {
                    // Combine all images from all files
                    let allImages = [];
                    
                    for (const [filename, images] of this.extractedImages) {
                        if (images && images.length > 0) {
                            allImages = allImages.concat(images.filter(img => img !== null));
                        }
                    }

                    // Display all images
                    this.displayImageGrid(allImages);
                    
                    // Show results section
                    document.getElementById('resultsSection').style.display = 'block';
                    
                    // Scroll to results
                    document.getElementById('resultsSection').scrollIntoView({ behavior: 'smooth' });
                    
                    this.showNotification('All images displayed successfully!', 'success');

                } catch (error) {
                    console.error('Error displaying images:', error);
                    this.showNotification(`Error displaying images: ${error.message}`, 'error');
                }
            }

            downloadAllImages() {
                // Create a zip file containing all images
                const zip = new JSZip();
                let imageCount = 0;
                
                for (const [filename, images] of this.extractedImages) {
                    if (images && images.length > 0) {
                        const cleanFilename = filename.replace('.pdf', '');
                        images.forEach((imageData, index) => {
                            if (imageData && imageData.base64Data) {
                                zip.file(`${cleanFilename}_page_${imageData.pageNumber}.png`, imageData.base64Data, {base64: true});
                                imageCount++;
                            }
                        });
                    }
                }
                
                if (imageCount > 0) {
                    zip.generateAsync({type: 'blob'}).then((content) => {
                        const url = URL.createObjectURL(content);
                        const a = document.createElement('a');
                        a.href = url;
                        a.download = `pdf-images-${new Date().toISOString().split('T')[0]}.zip`;
                        document.body.appendChild(a);
                        a.click();
                        document.body.removeChild(a);
                        URL.revokeObjectURL(url);
                        this.showNotification(`Downloaded ${imageCount} images as ZIP file!`, 'success');
                    });
                } else {
                    this.showNotification('No images to download', 'error');
                }
            }

            resetApplication() {
                // Clear all data
                this.uploadedFiles.clear();
                this.extractedImages.clear();
                this.fileStatuses.clear();

                // Reset UI
                document.getElementById('fileList').style.display = 'none';
                document.getElementById('fileItems').innerHTML = '';
                document.getElementById('resultsSection').style.display = 'none';
                document.getElementById('imagesContainer').innerHTML = '';
                document.getElementById('fileInput').value = '';

                // Reset process button
                this.updateProcessButtonState();

                this.showNotification('Application reset successfully', 'info');
            }
        }

        // ===== GLOBAL FUNCTIONS =====

        // Initialize the converter when page loads
        let pdfConverter;

        document.addEventListener('DOMContentLoaded', function() {
            pdfConverter = new PDFToImageConverter();
        });

        // Global functions for button onclick events
        function processAllFiles() {
            if (pdfConverter) {
                pdfConverter.processAllFiles();
            }
        }

        function downloadAllImages() {
            if (pdfConverter) {
                pdfConverter.downloadAllImages();
            }
        }

        function downloadImage(dataUrl, pageRef, filename) {
            const link = document.createElement('a');
            link.href = dataUrl;
            link.download = `${filename.replace('.pdf', '')}_${pageRef}.png`;
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        }

        function copyImageToClipboard(dataUrl, pageNumber) {
            // Convert data URL to blob and copy to clipboard
            fetch(dataUrl)
                .then(res => res.blob())
                .then(blob => {
                    const item = new ClipboardItem({ 'image/png': blob });
                    navigator.clipboard.write([item]).then(() => {
                        pdfConverter.showNotification(`Page ${pageNumber} copied to clipboard!`, 'success');
                    });
                })
                .catch(err => {
                    console.error('Failed to copy image:', err);
                    pdfConverter.showNotification('Failed to copy image to clipboard', 'error');
                });
        }

        function openImageModal(dataUrl, pageNumber) {
            if (pdfConverter) {
                pdfConverter.openImageModal(dataUrl, pageNumber);
            }
        }

        function resetApplication() {
            if (pdfConverter) {
                pdfConverter.resetApplication();
            }
        }
    </script>
</body>
</html>