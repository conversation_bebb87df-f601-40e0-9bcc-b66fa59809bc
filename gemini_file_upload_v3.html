<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PDF Document Analysis with Gemini AI</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 0;
            background-color: #f5f5f5;
        }

        /* Simple Navigation */
        .simple-nav {
            background: white;
            padding: 15px 20px;
            margin-bottom: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 15px;
        }

        .nav-brand-simple {
            font-size: 1.5em;
            font-weight: bold;
            color: #2e7d32;
            text-decoration: none;
        }

        .nav-links-simple {
            display: flex;
            gap: 20px;
            flex-wrap: wrap;
        }

        .nav-link-simple {
            color: #666;
            text-decoration: none;
            padding: 8px 16px;
            border-radius: 5px;
            transition: all 0.3s;
            font-weight: 500;
        }

        .nav-link-simple:hover {
            background: #e8f5e8;
            color: #2e7d32;
        }

        .nav-link-simple.active {
            background: #2e7d32;
            color: white;
        }

        .main-content {
            padding: 20px;
        }

        @media (max-width: 768px) {
            .simple-nav {
                flex-direction: column;
                text-align: center;
            }

            .nav-links-simple {
                justify-content: center;
            }
        }

        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }

        .upload-section {
            border: 2px dashed #ddd;
            border-radius: 10px;
            padding: 40px;
            text-align: center;
            margin-bottom: 30px;
            transition: border-color 0.3s;
        }

        .upload-section:hover {
            border-color: #4285f4;
        }

        .upload-section.dragover {
            border-color: #4285f4;
            background-color: #f0f8ff;
        }

        #fileInput {
            display: none;
        }

        .upload-btn {
            background-color: #4285f4;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px;
        }

        .upload-btn:hover {
            background-color: #3367d6;
        }

        .submit-btn {
            background-color: #34a853;
            color: white;
            padding: 15px 30px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 18px;
            width: 100%;
            margin-top: 20px;
        }

        .submit-btn:hover {
            background-color: #2d8f47;
        }

        .submit-btn:disabled {
            background-color: #ccc;
            cursor: not-allowed;
        }

        .file-info {
            margin-top: 20px;
            padding: 15px;
            background-color: #f8f9fa;
            border-radius: 5px;
            display: none;
        }

        .progress-section {
            margin-top: 30px;
            display: none;
        }

        .progress-bar {
            width: 100%;
            height: 20px;
            background-color: #e0e0e0;
            border-radius: 10px;
            overflow: hidden;
            margin-bottom: 10px;
        }

        .progress-fill {
            height: 100%;
            background-color: #4285f4;
            width: 0%;
            transition: width 0.3s;
        }

        .status-text {
            text-align: center;
            margin-bottom: 10px;
            font-weight: bold;
        }

        .results-section {
            margin-top: 30px;
            display: none;
        }

        .markdown-output {
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 5px;
            padding: 20px;
            max-height: 600px;
            overflow-y: auto;
            white-space: pre-wrap;
            font-family: 'Courier New', monospace;
            line-height: 1.5;
        }

        .error {
            color: #dc3545;
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }

        .success {
            color: #155724;
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }

        .page-set-info {
            background-color: #e3f2fd;
            border-left: 4px solid #2196f3;
            padding: 10px;
            margin: 10px 0;
        }

        .timing-info {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            gap: 15px;
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin: 15px 0;
            font-size: 14px;
        }

        .timing-info > div {
            text-align: center;
        }

        .detailed-progress {
            background-color: #fff;
            border: 1px solid #e9ecef;
            border-radius: 5px;
            padding: 15px;
            margin: 15px 0;
            max-height: 200px;
            overflow-y: auto;
        }

        .page-progress {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 5px 0;
            border-bottom: 1px solid #f0f0f0;
        }

        .page-progress:last-child {
            border-bottom: none;
        }

        .page-status {
            font-weight: bold;
        }

        .page-status.processing {
            color: #ffc107;
        }

        .page-status.completed {
            color: #28a745;
        }

        .page-status.error {
            color: #dc3545;
        }

        .page-timing {
            font-size: 12px;
            color: #6c757d;
        }

        /* Loading Spinner */
        .spinner {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            border-top-color: #fff;
            animation: spin 1s ease-in-out infinite;
            margin-left: 10px;
            vertical-align: middle;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }

        .submit-btn .spinner {
            border-color: rgba(255, 255, 255, 0.3);
            border-top-color: #fff;
        }

        .submit-btn:disabled .spinner {
            border-color: rgba(0, 0, 0, 0.3);
            border-top-color: #666;
        }

        .conversion-progress {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 5px;
            padding: 20px;
            margin: 20px 0;
        }

        .conversion-progress h4 {
            margin: 0 0 15px 0;
            color: #856404;
        }
    </style>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.11.174/pdf.min.js"></script>

</head>
<body>
    <!-- Simple Navigation -->
    <nav class="simple-nav">
        <a href="index.html" class="nav-brand-simple">🎯 PAIP</a>
        <div class="nav-links-simple">
            <a href="index.html" class="nav-link-simple">🏠 Home</a>
            <a href="gemini_file_upload_v3.html" class="nav-link-simple active">📄 Document Analysis</a>
            <a href="gemini_format_post.html" class="nav-link-simple">📊 PDF to CSV</a>
            <a href="paip_qc.html" class="nav-link-simple">🔍 Quality Check</a>
            <a href="html_markdownviewer.html" class="nav-link-simple">📝 Markdown Viewer</a>
        </div>
    </nav>

    <div class="main-content">
        <div class="container">
        <h1>PDF Document Analysis with Gemini AI</h1>

        <div class="upload-section" id="uploadSection">
            <h3>Upload PDF File</h3>
            <p>Drag and drop a PDF file here or click to select</p>
            <input type="file" id="fileInput" accept=".pdf" />
            <button class="upload-btn" onclick="document.getElementById('fileInput').click()">
                Choose PDF File
            </button>
            <div class="file-info" id="fileInfo">
                <strong>Selected File:</strong> <span id="fileName"></span><br>
                <strong>Size:</strong> <span id="fileSize"></span><br>
                <strong>Total Pages:</strong> <span id="totalPages"></span><br>
                <strong>Page Sets (5 pages each):</strong> <span id="pageSets"></span>
            </div>

            <div class="conversion-progress" id="conversionProgress" style="display: none;">
                <h4>Converting PDF Pages to Images...</h4>
                <div class="progress-bar">
                    <div class="progress-fill" id="conversionProgressFill"></div>
                </div>
                <div class="status-text" id="conversionStatus">Preparing conversion...</div>
                <div id="conversionDetails"></div>
            </div>
        </div>

        <button class="submit-btn" id="submitBtn" onclick="processFile()" disabled>
            <span id="buttonText">Select a PDF file first</span>
            <span id="loadingSpinner" class="spinner" style="display: none;"></span>
        </button>

        <div class="processing-note" style="text-align: center; margin-top: 10px; color: #666; font-size: 14px; display: none;" id="processingNote">
            <strong>Note:</strong> Document analysis may take several minutes per set of pages. Please be patient and do not close this window.
        </div>

        <div class="progress-section" id="progressSection">
            <div class="status-text" id="statusText">Processing...</div>
            <div class="progress-bar">
                <div class="progress-fill" id="progressFill"></div>
            </div>
            <div class="timing-info" id="timingInfo">
                <div><strong>Elapsed Time:</strong> <span id="elapsedTime">0s</span></div>
                <div><strong>Estimated Remaining:</strong> <span id="estimatedTime">Calculating...</span></div>
                <div><strong>Current Speed:</strong> <span id="processingSpeed">-</span></div>
            </div>
            <div id="currentSetInfo"></div>
            <div id="detailedProgress"></div>
        </div>

        <div class="results-section" id="resultsSection">
            <h3>Document Analysis Results (JSON Format)</h3>
            <div class="markdown-output" id="markdownOutput"></div>
            <button class="upload-btn" onclick="downloadResults()" style="margin-top: 15px;">
                Download as JSON
            </button>
        </div>
    </div>

    <script>
        // Gemini AI Configuration
        const GEMINI_API_KEY = 'AIzaSyApNBtEwylsW_q5zUOvIDP3pj87WDShSLA';
        const GEMINI_MODEL = 'gemini-2.5-flash-lite-preview-06-17';
        const GEMINI_API_URL = `https://generativelanguage.googleapis.com/v1beta/models/${GEMINI_MODEL}:generateContent?key=${GEMINI_API_KEY}`;

        let selectedFile = null;
        let pdfDocument = null;
        let totalPages = 0;
        let extractedText = '';
        let convertedPageSets = []; // Store pre-converted page images organized by sets
        let allPagesConverted = false;

        // Timing and progress tracking
        let processingStartTime = null;
        let currentSetStartTime = null;
        let totalPagesProcessed = 0;
        let timingInterval = null;

        // Set up PDF.js worker
        pdfjsLib.GlobalWorkerOptions.workerSrc = 'https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.11.174/pdf.worker.min.js';

        // File input handling
        document.getElementById('fileInput').addEventListener('change', handleFileSelect);

        // Drag and drop handling
        const uploadSection = document.getElementById('uploadSection');
        uploadSection.addEventListener('dragover', handleDragOver);
        uploadSection.addEventListener('dragleave', handleDragLeave);
        uploadSection.addEventListener('drop', handleDrop);

        function handleDragOver(e) {
            e.preventDefault();
            uploadSection.classList.add('dragover');
        }

        function handleDragLeave(e) {
            e.preventDefault();
            uploadSection.classList.remove('dragover');
        }

        function handleDrop(e) {
            e.preventDefault();
            uploadSection.classList.remove('dragover');

            const files = e.dataTransfer.files;
            if (files.length > 0 && files[0].type === 'application/pdf') {
                handleFile(files[0]);
            } else {
                showError('Please select a valid PDF file.');
                resetButton();
            }
        }

        function handleFileSelect(e) {
            const file = e.target.files[0];
            if (file && file.type === 'application/pdf') {
                handleFile(file);
            } else {
                showError('Please select a valid PDF file.');
                resetButton();
            }
        }

        function resetButton() {
            document.getElementById('submitBtn').disabled = true;
            document.getElementById('buttonText').textContent = 'Select a PDF file first';
            document.getElementById('loadingSpinner').style.display = 'none';
        }

        async function convertAllPagesToImages() {
            // Show conversion progress
            document.getElementById('conversionProgress').style.display = 'block';

            const totalSets = Math.ceil(totalPages / 5);
            convertedPageSets = [];

            try {
                for (let setIndex = 0; setIndex < totalSets; setIndex++) {
                    const startPage = setIndex * 5 + 1;
                    const endPage = Math.min((setIndex + 1) * 5, totalPages);

                    // Update conversion progress
                    const progress = ((setIndex + 1) / totalSets) * 100;
                    document.getElementById('conversionProgressFill').style.width = progress + '%';
                    document.getElementById('conversionStatus').textContent =
                        `Converting set ${setIndex + 1} of ${totalSets} (Pages ${startPage}-${endPage})`;

                    // Convert pages in this set
                    const setImages = [];
                    for (let pageNum = startPage; pageNum <= endPage; pageNum++) {
                        try {
                            // Update detailed status
                            document.getElementById('conversionDetails').innerHTML =
                                `<div>Converting page ${pageNum} to image...</div>`;

                            const page = await pdfDocument.getPage(pageNum);
                            const scale = 2.0; // High resolution for better text recognition
                            const viewport = page.getViewport({ scale });

                            // Create canvas for image conversion
                            const canvas = document.createElement('canvas');
                            const context = canvas.getContext('2d');
                            canvas.height = viewport.height;
                            canvas.width = viewport.width;

                            // Render PDF page to canvas
                            await page.render({
                                canvasContext: context,
                                viewport: viewport
                            }).promise;

                            // Convert to base64 PNG image
                            const imageData = canvas.toDataURL('image/png').split(',')[1];
                            setImages.push({
                                pageNumber: pageNum,
                                data: imageData
                            });

                        } catch (error) {
                            console.error(`Error converting page ${pageNum}:`, error);
                            // Add placeholder for failed page
                            setImages.push({
                                pageNumber: pageNum,
                                data: null,
                                error: error.message
                            });
                        }
                    }

                    // Store the converted set
                    convertedPageSets.push({
                        setNumber: setIndex + 1,
                        startPage: startPage,
                        endPage: endPage,
                        images: setImages
                    });

                    // Small delay to prevent UI blocking
                    await new Promise(resolve => setTimeout(resolve, 100));
                }

                // Conversion completed
                allPagesConverted = true;
                document.getElementById('conversionProgress').style.display = 'none';
                document.getElementById('submitBtn').disabled = false;
                document.getElementById('buttonText').textContent = 'Analyze Document';

                showSuccess(`All ${totalPages} pages converted to images successfully! Ready to analyze.`);

            } catch (error) {
                showError('Error converting pages to images: ' + error.message);
                document.getElementById('conversionProgress').style.display = 'none';
                resetButton();
            }
        }

        async function handleFile(file) {
            selectedFile = file;
            allPagesConverted = false;
            convertedPageSets = [];

            try {
                // Load PDF to get page count
                const arrayBuffer = await file.arrayBuffer();
                pdfDocument = await pdfjsLib.getDocument(arrayBuffer).promise;
                totalPages = pdfDocument.numPages;

                // Update UI
                document.getElementById('fileName').textContent = file.name;
                document.getElementById('fileSize').textContent = formatFileSize(file.size);
                document.getElementById('totalPages').textContent = totalPages;
                document.getElementById('pageSets').textContent = Math.ceil(totalPages / 5);
                document.getElementById('fileInfo').style.display = 'block';

                // Keep button disabled until images are converted
                document.getElementById('submitBtn').disabled = true;
                document.getElementById('buttonText').textContent = 'Converting pages to images...';

                showSuccess(`PDF loaded successfully! ${totalPages} pages found. Converting to images...`);

                // Start converting all pages to images immediately
                await convertAllPagesToImages();

            } catch (error) {
                showError('Error loading PDF: ' + error.message);
                resetButton();
            }
        }

        async function processFile() {
            if (!selectedFile || !pdfDocument || !allPagesConverted) {
                showError('Please wait for page conversion to complete first.');
                return;
            }

            // Initialize timing
            processingStartTime = Date.now();
            totalPagesProcessed = 0;

            // Reset results
            extractedText = '';
            document.getElementById('markdownOutput').textContent = '';
            document.getElementById('resultsSection').style.display = 'none';

            // Show progress section and update button
            document.getElementById('progressSection').style.display = 'block';
            document.getElementById('submitBtn').disabled = true;
            document.getElementById('buttonText').textContent = 'Analyzing...';
            document.getElementById('loadingSpinner').style.display = 'inline-block';
            document.getElementById('processingNote').style.display = 'block';

            // Start timing updates
            startTimingUpdates();

            try {
                const totalSets = convertedPageSets.length;

                // Initialize detailed progress display
                document.getElementById('detailedProgress').innerHTML =
                    '<div class="detailed-progress"><h4>Processing Details:</h4><div id="progressDetails"></div></div>';

                for (let setIndex = 0; setIndex < totalSets; setIndex++) {
                    const pageSet = convertedPageSets[setIndex];
                    const { setNumber, startPage, endPage, images } = pageSet;
                    currentSetStartTime = Date.now();

                    // Update main progress
                    const overallProgress = (totalPagesProcessed / totalPages) * 100;
                    document.getElementById('progressFill').style.width = overallProgress + '%';
                    document.getElementById('statusText').textContent =
                        `Processing set ${setNumber} of ${totalSets} (Pages ${startPage}-${endPage})`;

                    // Show current set info
                    document.getElementById('currentSetInfo').innerHTML =
                        `<div class="page-set-info">Processing pages ${startPage} to ${endPage} - AI is analyzing (may take several minutes)...</div>`;

                    // Add set to detailed progress
                    addSetToProgress(setNumber, startPage, endPage, 'processing');

                    // Update individual page status (images already converted)
                    for (let i = 0; i < images.length; i++) {
                        const image = images[i];
                        if (image.error) {
                            updatePageProgress(setNumber, image.pageNumber, 'error', `Conversion error: ${image.error}`);
                        } else {
                            updatePageProgress(setNumber, image.pageNumber, 'completed', 'Image ready');
                        }
                    }

                    // Send pre-converted images to Gemini AI
                    let setText = '';
                    try {
                        updateSetProgress(setNumber, 'processing', null, 'Sending to Gemini AI - please wait...');

                        setText = await analyzePageSetWithGemini(images, setNumber, startPage, endPage);

                        // Update set status
                        const setTime = Date.now() - currentSetStartTime;
                        updateSetProgress(setNumber, 'completed', setTime);

                    } catch (setError) {
                        console.error(`Error processing set ${setNumber}:`, setError);
                        const setTime = Date.now() - currentSetStartTime;
                        updateSetProgress(setNumber, 'error', setTime, `Set error: ${setError.message}`);

                        // Continue with other sets even if this one fails
                        setText = `\n\n**Pages ${startPage}-${endPage}**\n\n*Error processing this set: ${setError.message}*\n\n*Processing continued with remaining pages.*\n\n`;
                    }

                    // Append to full text (always append something, even if it's an error message)
                    if (setText) {
                        extractedText += `\n\n## Pages ${startPage}-${endPage}\n\n${setText}`;
                    }

                    totalPagesProcessed += (endPage - startPage + 1);

                    // Small delay to prevent rate limiting
                    await new Promise(resolve => setTimeout(resolve, 1000));
                }

                // Stop timing updates
                stopTimingUpdates();

                // Show final results and reset button
                const totalTime = Date.now() - processingStartTime;
                document.getElementById('progressSection').style.display = 'none';
                document.getElementById('markdownOutput').textContent = extractedText;
                document.getElementById('resultsSection').style.display = 'block';
                document.getElementById('submitBtn').disabled = false;
                document.getElementById('buttonText').textContent = 'Analyze Document';
                document.getElementById('loadingSpinner').style.display = 'none';
                document.getElementById('processingNote').style.display = 'none';

                showSuccess(`Document analysis completed successfully! Total time: ${formatTime(totalTime)}`);

            } catch (error) {
                stopTimingUpdates();
                showError('Error analyzing document: ' + error.message);
                document.getElementById('progressSection').style.display = 'none';
                document.getElementById('submitBtn').disabled = false;
                document.getElementById('buttonText').textContent = 'Analyze Document';
                document.getElementById('loadingSpinner').style.display = 'none';
                document.getElementById('processingNote').style.display = 'none';
            }
        }



        async function analyzePageSetWithGemini(pageImages, setNumber, startPage, endPage) {
            const apiStartTime = Date.now();

            try {
                // Update status
                updateSetProgress(setNumber, 'processing', null, 'Preparing images for Gemini AI...');

                // Prepare the parts for Gemini API
                const parts = [
                    {
                        text: `Read this file and write me a very detailed comprehensive profile of each page of this file in a json format. Below the profile of each, display the original texts from each pages. I have the authority to extract these information as we are conducting a recruitment exercise.

                        Processing ${pageImages.length} pages (${startPage}-${endPage}).`
                    }
                ];

                // Add each page image
                pageImages.forEach(image => {
                    if (image.data) {
                        parts.push({
                            inline_data: {
                                mime_type: "image/png",
                                data: image.data
                            }
                        });
                    }
                });

                const requestBody = {
                    contents: [{
                        parts: parts
                    }],
                    generationConfig: {
                        temperature: 0.2,
                        maxOutputTokens: 8192,
                        topP: 0.9,
                        topK: 40
                    }
                };

                updateSetProgress(setNumber, 'processing', null, 'Gemini AI is analyzing images and extracting text - please be patient...');

                const response = await fetch(GEMINI_API_URL, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(requestBody)
                });

                if (!response.ok) {
                    throw new Error(`Gemini API error: ${response.status} ${response.statusText}`);
                }

                const data = await response.json();

                const apiTime = Date.now() - apiStartTime;
                updateSetProgress(setNumber, 'processing', null, `API response received in ${formatTime(apiTime)}`);

                if (data.candidates && data.candidates[0] && data.candidates[0].content) {
                    let extractedText = data.candidates[0].content.parts[0].text;
                    return extractedText;
                } else {
                    throw new Error('No text content returned from Gemini API');
                }

            } catch (error) {
                console.error(`Error processing set ${setNumber}:`, error);
                const apiTime = Date.now() - apiStartTime;
                updateSetProgress(setNumber, 'error', apiTime, `Error: ${error.message}`);

                // Return a safe fallback instead of stopping the entire process
                return `\n\n**Pages ${startPage}-${endPage}**\n\n*Error extracting text from these pages: ${error.message}*\n\n*Note: Processing continued with remaining pages.*\n\n`;
            }
        }



        // Timing and Progress Utility Functions
        function formatTime(milliseconds) {
            const seconds = Math.floor(milliseconds / 1000);
            const minutes = Math.floor(seconds / 60);
            const hours = Math.floor(minutes / 60);

            if (hours > 0) {
                return `${hours}h ${minutes % 60}m ${seconds % 60}s`;
            } else if (minutes > 0) {
                return `${minutes}m ${seconds % 60}s`;
            } else {
                return `${seconds}s`;
            }
        }

        function startTimingUpdates() {
            timingInterval = setInterval(updateTimingDisplay, 1000);
        }

        function stopTimingUpdates() {
            if (timingInterval) {
                clearInterval(timingInterval);
                timingInterval = null;
            }
        }

        function updateTimingDisplay() {
            if (!processingStartTime) return;

            const elapsed = Date.now() - processingStartTime;
            document.getElementById('elapsedTime').textContent = formatTime(elapsed);

            // Calculate estimated remaining time
            if (totalPagesProcessed > 0) {
                const avgTimePerPage = elapsed / totalPagesProcessed;
                const remainingPages = totalPages - totalPagesProcessed;
                const estimatedRemaining = avgTimePerPage * remainingPages;

                document.getElementById('estimatedTime').textContent = formatTime(estimatedRemaining);

                // Calculate processing speed
                const pagesPerMinute = (totalPagesProcessed / elapsed) * 60000;
                document.getElementById('processingSpeed').textContent =
                    `${pagesPerMinute.toFixed(1)} pages/min`;
            }
        }

        function addSetToProgress(setNumber, startPage, endPage, status) {
            const progressDetails = document.getElementById('progressDetails');
            const setDiv = document.createElement('div');
            setDiv.id = `set-${setNumber}`;
            setDiv.className = 'page-progress';
            setDiv.innerHTML = `
                <div>
                    <span class="page-status ${status}">Set ${setNumber} (Pages ${startPage}-${endPage})</span>
                    <div id="set-${setNumber}-pages"></div>
                </div>
                <div class="page-timing" id="set-${setNumber}-timing">Starting...</div>
            `;
            progressDetails.appendChild(setDiv);

            // Add individual page entries
            const pagesDiv = document.getElementById(`set-${setNumber}-pages`);
            for (let page = startPage; page <= endPage; page++) {
                const pageDiv = document.createElement('div');
                pageDiv.id = `page-${page}`;
                pageDiv.className = 'page-progress';
                pageDiv.style.marginLeft = '20px';
                pageDiv.style.fontSize = '12px';
                pageDiv.innerHTML = `
                    <span class="page-status">Page ${page}</span>
                    <span class="page-timing" id="page-${page}-timing">Waiting...</span>
                `;
                pagesDiv.appendChild(pageDiv);
            }
        }

        function updateSetProgress(setNumber, status, time, message) {
            const setElement = document.getElementById(`set-${setNumber}`);
            if (setElement) {
                const statusElement = setElement.querySelector('.page-status');
                statusElement.className = `page-status ${status}`;

                const timingElement = document.getElementById(`set-${setNumber}-timing`);
                if (time) {
                    timingElement.textContent = `${formatTime(time)} - ${message || ''}`;
                } else if (message) {
                    timingElement.textContent = message;
                }
            }
        }

        function updatePageProgress(setNumber, pageNumber, status, message) {
            const pageElement = document.getElementById(`page-${pageNumber}`);
            if (pageElement) {
                const statusElement = pageElement.querySelector('.page-status');
                statusElement.className = `page-status ${status}`;

                const timingElement = document.getElementById(`page-${pageNumber}-timing`);
                timingElement.textContent = message || status;
            }
        }

        // Utility functions
        function formatFileSize(bytes) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }

        function showError(message) {
            // Remove existing messages
            const existingMessages = document.querySelectorAll('.error, .success');
            existingMessages.forEach(msg => msg.remove());

            const errorDiv = document.createElement('div');
            errorDiv.className = 'error';
            errorDiv.textContent = message;
            document.querySelector('.container').insertBefore(errorDiv, document.querySelector('.upload-section'));

            // Auto-remove after 5 seconds
            setTimeout(() => {
                if (errorDiv.parentNode) {
                    errorDiv.remove();
                }
            }, 5000);
        }

        function showSuccess(message) {
            // Remove existing messages
            const existingMessages = document.querySelectorAll('.error, .success');
            existingMessages.forEach(msg => msg.remove());

            const successDiv = document.createElement('div');
            successDiv.className = 'success';
            successDiv.textContent = message;
            document.querySelector('.container').insertBefore(successDiv, document.querySelector('.upload-section'));

            // Auto-remove after 3 seconds
            setTimeout(() => {
                if (successDiv.parentNode) {
                    successDiv.remove();
                }
            }, 3000);
        }

        function downloadResults() {
            if (!extractedText) {
                showError('No results to download. Please analyze document first.');
                return;
            }

            const blob = new Blob([extractedText], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `document_analysis_${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.json`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
        }

        // Initialize the application
        document.addEventListener('DOMContentLoaded', function() {
            console.log('PDF Document Analysis with Gemini AI - Ready');
        });
    </script>
    </div> <!-- End main-content -->
</body>
</html>