<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SVG Viewer</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            padding: 20px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }

        .header p {
            opacity: 0.8;
            font-size: 1.1em;
        }

        .controls {
            padding: 20px;
            background: #f8f9fa;
            border-bottom: 1px solid #e9ecef;
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
            align-items: center;
            justify-content: center;
        }

        .file-input-wrapper {
            position: relative;
            overflow: hidden;
            display: inline-block;
        }

        .file-input-wrapper input[type=file] {
            position: absolute;
            left: -9999px;
        }

        .file-input-label {
            background: #28a745;
            color: white;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-weight: 600;
            display: inline-block;
        }

        .file-input-label:hover {
            background: #218838;
            transform: translateY(-2px);
        }

        .control-btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 12px 20px;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 600;
            transition: all 0.3s ease;
            font-size: 14px;
        }

        .control-btn:hover {
            background: #0056b3;
            transform: translateY(-2px);
        }

        .control-btn:disabled {
            background: #6c757d;
            cursor: not-allowed;
            transform: none;
        }

        .file-info {
            background: #e3f2fd;
            padding: 15px;
            margin: 0 20px;
            border-radius: 8px;
            display: none;
        }

        .file-info h3 {
            color: #1976d2;
            margin-bottom: 8px;
        }

        .file-info p {
            margin: 4px 0;
            color: #424242;
        }

        .viewer-container {
            position: relative;
            height: 600px;
            margin: 20px;
            border: 2px dashed #dee2e6;
            border-radius: 10px;
            overflow: hidden;
            background: #f8f9fa;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .viewer-container.has-content {
            border: 2px solid #28a745;
            background: white;
        }

        .placeholder {
            text-align: center;
            color: #6c757d;
            font-size: 1.2em;
        }

        .placeholder i {
            font-size: 4em;
            margin-bottom: 20px;
            display: block;
        }

        #svgDisplay {
            width: 100%;
            height: 100%;
            cursor: grab;
            transition: transform 0.1s ease;
        }

        #svgDisplay:active {
            cursor: grabbing;
        }

        .zoom-info {
            position: absolute;
            top: 10px;
            right: 10px;
            background: rgba(0,0,0,0.7);
            color: white;
            padding: 8px 12px;
            border-radius: 6px;
            font-size: 14px;
            display: none;
        }

        .footer {
            text-align: center;
            padding: 20px;
            background: #f8f9fa;
            color: #6c757d;
            border-top: 1px solid #e9ecef;
        }

        @media (max-width: 768px) {
            .controls {
                flex-direction: column;
            }
            
            .header h1 {
                font-size: 2em;
            }
            
            .viewer-container {
                height: 400px;
                margin: 10px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎨 SVG Viewer</h1>
            <p>Upload and view SVG files with zoom and pan controls</p>
        </div>

        <div class="controls">
            <div class="file-input-wrapper">
                <input type="file" id="fileInput" accept=".svg,image/svg+xml">
                <label for="fileInput" class="file-input-label">📁 Choose SVG File</label>
            </div>

            <button class="control-btn" id="pasteSvg">📋 Paste SVG Code</button>
            <button class="control-btn" id="zoomIn" disabled>🔍+ Zoom In</button>
            <button class="control-btn" id="zoomOut" disabled>🔍- Zoom Out</button>
            <button class="control-btn" id="fitToScreen" disabled>📐 Fit to Screen</button>
            <button class="control-btn" id="actualSize" disabled>📏 Actual Size</button>
            <button class="control-btn" id="resetView" disabled>🔄 Reset View</button>
            <button class="control-btn" id="downloadSvg" disabled>💾 Download SVG</button>
            <button class="control-btn" id="exportPng" disabled>🖼️ Export PNG</button>
        </div>

        <div class="file-info" id="fileInfo">
            <h3>📄 File Information</h3>
            <p><strong>Name:</strong> <span id="fileName"></span></p>
            <p><strong>Size:</strong> <span id="fileSize"></span></p>
            <p><strong>Dimensions:</strong> <span id="fileDimensions"></span></p>
        </div>

        <div class="viewer-container" id="viewerContainer">
            <div class="placeholder">
                <i>🎨</i>
                <p>Select an SVG file, paste SVG code, or drag & drop</p>
                <p style="font-size: 0.9em; margin-top: 10px;">Supports multiple input methods</p>
            </div>
            <div id="svgDisplay"></div>
            <div class="zoom-info" id="zoomInfo">100%</div>
        </div>

        <!-- SVG Code Input Modal -->
        <div id="svgModal" style="display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); z-index: 1000;">
            <div style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); background: white; padding: 30px; border-radius: 15px; width: 90%; max-width: 600px; max-height: 80vh; overflow-y: auto;">
                <h3 style="margin-bottom: 20px; color: #2c3e50;">📋 Paste SVG Code</h3>
                <textarea id="svgCodeInput" placeholder="Paste your SVG code here..." style="width: 100%; height: 300px; border: 2px solid #dee2e6; border-radius: 8px; padding: 15px; font-family: 'Courier New', monospace; font-size: 14px; resize: vertical;"></textarea>
                <div style="margin-top: 20px; text-align: right;">
                    <button id="cancelPaste" style="background: #6c757d; color: white; border: none; padding: 12px 20px; border-radius: 8px; margin-right: 10px; cursor: pointer;">Cancel</button>
                    <button id="loadSvgCode" style="background: #28a745; color: white; border: none; padding: 12px 20px; border-radius: 8px; cursor: pointer;">Load SVG</button>
                </div>
            </div>
        </div>

        <div class="footer">
            <p>SVG Viewer - Developed by Dakoii Systems © 2025 | Powered by Dakoii AI</p>
        </div>
    </div>

    <script>
        class SVGViewer {
            constructor() {
                this.currentSVG = null;
                this.currentScale = 1;
                this.currentX = 0;
                this.currentY = 0;
                this.isDragging = false;
                this.lastMouseX = 0;
                this.lastMouseY = 0;
                this.originalViewBox = null;
                this.currentFile = null;
                
                this.initializeElements();
                this.bindEvents();
            }

            initializeElements() {
                this.fileInput = document.getElementById('fileInput');
                this.svgDisplay = document.getElementById('svgDisplay');
                this.viewerContainer = document.getElementById('viewerContainer');
                this.fileInfo = document.getElementById('fileInfo');
                this.zoomInfo = document.getElementById('zoomInfo');
                
                // Control buttons
                this.zoomInBtn = document.getElementById('zoomIn');
                this.zoomOutBtn = document.getElementById('zoomOut');
                this.fitToScreenBtn = document.getElementById('fitToScreen');
                this.actualSizeBtn = document.getElementById('actualSize');
                this.resetViewBtn = document.getElementById('resetView');
                this.downloadBtn = document.getElementById('downloadSvg');
                this.exportPngBtn = document.getElementById('exportPng');
                this.pasteSvgBtn = document.getElementById('pasteSvg');

                // Modal elements
                this.svgModal = document.getElementById('svgModal');
                this.svgCodeInput = document.getElementById('svgCodeInput');
                this.loadSvgCodeBtn = document.getElementById('loadSvgCode');
                this.cancelPasteBtn = document.getElementById('cancelPaste');
            }

            bindEvents() {
                // File input
                this.fileInput.addEventListener('change', (e) => this.handleFileSelect(e));
                
                // Drag and drop
                this.viewerContainer.addEventListener('dragover', (e) => {
                    e.preventDefault();
                    this.viewerContainer.style.borderColor = '#007bff';
                });
                
                this.viewerContainer.addEventListener('dragleave', (e) => {
                    e.preventDefault();
                    this.viewerContainer.style.borderColor = '#dee2e6';
                });
                
                this.viewerContainer.addEventListener('drop', (e) => {
                    e.preventDefault();
                    this.viewerContainer.style.borderColor = '#28a745';
                    const files = e.dataTransfer.files;
                    if (files.length > 0 && files[0].type === 'image/svg+xml') {
                        this.loadSVGFile(files[0]);
                    }
                });

                // Control buttons
                this.zoomInBtn.addEventListener('click', () => this.zoomIn());
                this.zoomOutBtn.addEventListener('click', () => this.zoomOut());
                this.fitToScreenBtn.addEventListener('click', () => this.fitToScreen());
                this.actualSizeBtn.addEventListener('click', () => this.actualSize());
                this.resetViewBtn.addEventListener('click', () => this.resetView());
                this.downloadBtn.addEventListener('click', () => this.downloadSVG());
                this.exportPngBtn.addEventListener('click', () => this.exportToPNG());
                this.pasteSvgBtn.addEventListener('click', () => this.showSvgModal());

                // Modal buttons
                this.loadSvgCodeBtn.addEventListener('click', () => this.loadSvgFromCode());
                this.cancelPasteBtn.addEventListener('click', () => this.hideSvgModal());

                // Close modal when clicking outside
                this.svgModal.addEventListener('click', (e) => {
                    if (e.target === this.svgModal) {
                        this.hideSvgModal();
                    }
                });

                // Mouse events for panning
                this.svgDisplay.addEventListener('mousedown', (e) => this.startDrag(e));
                this.svgDisplay.addEventListener('mousemove', (e) => this.drag(e));
                this.svgDisplay.addEventListener('mouseup', () => this.endDrag());
                this.svgDisplay.addEventListener('mouseleave', () => this.endDrag());

                // Wheel event for zooming
                this.svgDisplay.addEventListener('wheel', (e) => this.handleWheel(e));
            }

            handleFileSelect(event) {
                const file = event.target.files[0];
                if (file && file.type === 'image/svg+xml') {
                    this.loadSVGFile(file);
                }
            }

            loadSVGFile(file) {
                this.currentFile = file;
                const reader = new FileReader();
                
                reader.onload = (e) => {
                    const svgContent = e.target.result;
                    this.displaySVG(svgContent);
                    this.updateFileInfo(file);
                    this.enableControls();
                };
                
                reader.readAsText(file);
            }

            displaySVG(svgContent) {
                this.svgDisplay.innerHTML = svgContent;
                this.currentSVG = this.svgDisplay.querySelector('svg');
                
                if (this.currentSVG) {
                    // Store original viewBox
                    this.originalViewBox = this.currentSVG.getAttribute('viewBox');
                    
                    // Set up SVG for manipulation
                    this.currentSVG.style.width = '100%';
                    this.currentSVG.style.height = '100%';
                    
                    this.viewerContainer.classList.add('has-content');
                    this.viewerContainer.querySelector('.placeholder').style.display = 'none';
                    this.zoomInfo.style.display = 'block';
                    
                    this.resetView();
                }
            }

            updateFileInfo(file) {
                document.getElementById('fileName').textContent = file.name;
                document.getElementById('fileSize').textContent = this.formatFileSize(file.size);
                
                // Get SVG dimensions
                if (this.currentSVG) {
                    const width = this.currentSVG.getAttribute('width') || 'auto';
                    const height = this.currentSVG.getAttribute('height') || 'auto';
                    document.getElementById('fileDimensions').textContent = `${width} × ${height}`;
                }
                
                this.fileInfo.style.display = 'block';
            }

            formatFileSize(bytes) {
                if (bytes === 0) return '0 Bytes';
                const k = 1024;
                const sizes = ['Bytes', 'KB', 'MB', 'GB'];
                const i = Math.floor(Math.log(bytes) / Math.log(k));
                return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
            }

            enableControls() {
                const buttons = [this.zoomInBtn, this.zoomOutBtn, this.fitToScreenBtn,
                               this.actualSizeBtn, this.resetViewBtn, this.downloadBtn, this.exportPngBtn];
                buttons.forEach(btn => btn.disabled = false);
            }

            showSvgModal() {
                this.svgModal.style.display = 'block';
                this.svgCodeInput.focus();
            }

            hideSvgModal() {
                this.svgModal.style.display = 'none';
                this.svgCodeInput.value = '';
            }

            loadSvgFromCode() {
                const svgCode = this.svgCodeInput.value.trim();
                if (!svgCode) {
                    alert('Please paste SVG code first.');
                    return;
                }

                // Validate if it's valid SVG
                if (!svgCode.includes('<svg') || !svgCode.includes('</svg>')) {
                    alert('Invalid SVG code. Please make sure you paste complete SVG markup.');
                    return;
                }

                this.displaySVG(svgCode);
                this.updateFileInfoFromCode(svgCode);
                this.enableControls();
                this.hideSvgModal();
            }

            updateFileInfoFromCode(svgCode) {
                document.getElementById('fileName').textContent = 'Pasted SVG Code';
                document.getElementById('fileSize').textContent = this.formatFileSize(new Blob([svgCode]).size);

                // Get SVG dimensions from code
                if (this.currentSVG) {
                    const width = this.currentSVG.getAttribute('width') || 'auto';
                    const height = this.currentSVG.getAttribute('height') || 'auto';
                    document.getElementById('fileDimensions').textContent = `${width} × ${height}`;
                }

                this.fileInfo.style.display = 'block';
            }

            zoomIn() {
                this.currentScale *= 1.2;
                this.updateTransform();
            }

            zoomOut() {
                this.currentScale /= 1.2;
                this.updateTransform();
            }

            fitToScreen() {
                if (!this.currentSVG) return;
                
                const containerRect = this.viewerContainer.getBoundingClientRect();
                const svgRect = this.currentSVG.getBoundingClientRect();
                
                const scaleX = containerRect.width / svgRect.width;
                const scaleY = containerRect.height / svgRect.height;
                
                this.currentScale = Math.min(scaleX, scaleY) * 0.9;
                this.currentX = 0;
                this.currentY = 0;
                this.updateTransform();
            }

            actualSize() {
                this.currentScale = 1;
                this.currentX = 0;
                this.currentY = 0;
                this.updateTransform();
            }

            resetView() {
                this.currentScale = 1;
                this.currentX = 0;
                this.currentY = 0;
                this.updateTransform();
            }

            updateTransform() {
                if (!this.currentSVG) return;
                
                this.currentSVG.style.transform = 
                    `translate(${this.currentX}px, ${this.currentY}px) scale(${this.currentScale})`;
                
                this.zoomInfo.textContent = Math.round(this.currentScale * 100) + '%';
            }

            startDrag(e) {
                this.isDragging = true;
                this.lastMouseX = e.clientX;
                this.lastMouseY = e.clientY;
                e.preventDefault();
            }

            drag(e) {
                if (!this.isDragging) return;
                
                const deltaX = e.clientX - this.lastMouseX;
                const deltaY = e.clientY - this.lastMouseY;
                
                this.currentX += deltaX;
                this.currentY += deltaY;
                
                this.lastMouseX = e.clientX;
                this.lastMouseY = e.clientY;
                
                this.updateTransform();
            }

            endDrag() {
                this.isDragging = false;
            }

            handleWheel(e) {
                e.preventDefault();
                
                if (e.deltaY < 0) {
                    this.zoomIn();
                } else {
                    this.zoomOut();
                }
            }

            downloadSVG() {
                if (!this.currentSVG) return;

                let svgData, filename;

                if (this.currentFile) {
                    // If loaded from file, use original file
                    const url = URL.createObjectURL(this.currentFile);
                    const a = document.createElement('a');
                    a.href = url;
                    a.download = this.currentFile.name;
                    document.body.appendChild(a);
                    a.click();
                    document.body.removeChild(a);
                    URL.revokeObjectURL(url);
                } else {
                    // If loaded from pasted code, create new file
                    const svgString = new XMLSerializer().serializeToString(this.currentSVG);
                    const blob = new Blob([svgString], { type: 'image/svg+xml' });
                    const url = URL.createObjectURL(blob);
                    const a = document.createElement('a');
                    a.href = url;
                    a.download = 'exported-svg.svg';
                    document.body.appendChild(a);
                    a.click();
                    document.body.removeChild(a);
                    URL.revokeObjectURL(url);
                }
            }

            exportToPNG() {
                if (!this.currentSVG) return;

                try {
                    // Create a canvas element
                    const canvas = document.createElement('canvas');
                    const ctx = canvas.getContext('2d');

                    // Get SVG dimensions
                    const svgRect = this.currentSVG.getBoundingClientRect();
                    let svgWidth = this.currentSVG.viewBox?.baseVal?.width ||
                                  parseInt(this.currentSVG.getAttribute('width')) ||
                                  svgRect.width || 800;
                    let svgHeight = this.currentSVG.viewBox?.baseVal?.height ||
                                   parseInt(this.currentSVG.getAttribute('height')) ||
                                   svgRect.height || 600;

                    // Set canvas size (2x resolution for better quality)
                    const scale = 2;
                    canvas.width = svgWidth * scale;
                    canvas.height = svgHeight * scale;

                    // Scale the context to maintain aspect ratio
                    ctx.scale(scale, scale);

                    // Get SVG string and create data URL
                    const svgString = new XMLSerializer().serializeToString(this.currentSVG);

                    // Ensure SVG has proper namespace and encoding
                    const svgWithNamespace = svgString.includes('xmlns') ?
                        svgString :
                        svgString.replace('<svg', '<svg xmlns="http://www.w3.org/2000/svg"');

                    // Create data URL directly (avoids CORS issues)
                    const svgDataUrl = 'data:image/svg+xml;charset=utf-8,' + encodeURIComponent(svgWithNamespace);

                    const img = new Image();
                    img.onload = () => {
                        try {
                            // Clear canvas with white background
                            ctx.fillStyle = 'white';
                            ctx.fillRect(0, 0, svgWidth, svgHeight);

                            // Draw the SVG image
                            ctx.drawImage(img, 0, 0, svgWidth, svgHeight);

                            // Convert canvas to PNG and download
                            canvas.toBlob((blob) => {
                                if (blob) {
                                    const pngUrl = URL.createObjectURL(blob);
                                    const a = document.createElement('a');
                                    a.href = pngUrl;
                                    a.download = this.currentFile ?
                                        this.currentFile.name.replace('.svg', '.png') :
                                        'exported-svg.png';
                                    document.body.appendChild(a);
                                    a.click();
                                    document.body.removeChild(a);
                                    URL.revokeObjectURL(pngUrl);
                                } else {
                                    alert('Failed to create PNG. Your browser might not support this feature.');
                                }
                            }, 'image/png');
                        } catch (error) {
                            console.error('Canvas drawing error:', error);
                            alert('Error drawing SVG to canvas. The SVG might be too complex or contain unsupported features.');
                        }
                    };

                    img.onerror = () => {
                        console.error('Image loading error');
                        alert('Error loading SVG for conversion. The SVG might contain external resources or invalid markup.');
                    };

                    // Use data URL to avoid CORS issues
                    img.src = svgDataUrl;

                } catch (error) {
                    console.error('PNG export error:', error);
                    alert('Error exporting to PNG: ' + error.message);
                }
            }
        }

        // Initialize the SVG Viewer when the page loads
        document.addEventListener('DOMContentLoaded', () => {
            new SVGViewer();
        });
    </script>
</body>
</html>
