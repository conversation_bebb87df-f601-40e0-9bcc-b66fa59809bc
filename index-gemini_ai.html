<!DOCTYPE html>
<!-- 
    Smart Test Grading System
    Using Google Gemini 2.0 Flash for enhanced text extraction and analysis
    Temperature set to 0 for maximum accuracy and consistency
    Last updated: 2024
-->
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Smart Test Grading System</title>
    <!-- Favicon -->
    <link rel="icon" href="favicon.ico" type="image/x-icon">
    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <!-- Font Awesome for icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- PDF.js library for PDF rendering -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.11.174/pdf.min.js"></script>
    <script>
        // Set PDF.js worker path
        pdfjsLib.GlobalWorkerOptions.workerSrc = 'https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.11.174/pdf.worker.min.js';
    </script>
    <style>
        :root {
            --primary-color: #2ecc71;
            --primary-hover: #27ae60;
            --secondary-color: #1abc9c;
            --accent-color: #3498db;
            --success-color: #2ecc71;
            --warning-color: #f39c12;
            --danger-color: #e74c3c;
            --text-primary: #2c3e50;
            --text-secondary: #7f8c8d;
            --bg-light: #ecf0f1;
            --bg-white: #ffffff;
            --border-color: #e0e0e0;
            --shadow-sm: 0 2px 5px rgba(0, 0, 0, 0.05);
            --shadow-md: 0 4px 10px rgba(0, 0, 0, 0.08);
            --shadow-lg: 0 10px 20px rgba(0, 0, 0, 0.1);
            --radius-sm: 0.25rem;
            --radius-md: 0.5rem;
            --radius-lg: 1rem;
            --transition-speed: 0.3s;
        }

        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }

        body {
            font-family: 'Poppins', sans-serif;
            color: var(--text-primary);
            background: linear-gradient(135deg, #e0f7fa 0%, #e8f5e9 100%);
            line-height: 1.6;
            margin: 0;
            min-height: 100vh;
            padding: 2rem;
            background-attachment: fixed;
        }

        .container {
            max-width: 1100px;
            margin: 0 auto;
            background-color: var(--bg-white);
            border-radius: var(--radius-lg);
            box-shadow: var(--shadow-lg);
            padding: 2rem;
            margin-bottom: 2rem;
            overflow: hidden;
            position: relative;
            transition: transform var(--transition-speed), box-shadow var(--transition-speed);
        }

        .container:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 30px rgba(46, 204, 113, 0.15);
        }

        .header {
            text-align: center;
            margin-bottom: 2.5rem;
            padding-bottom: 1.5rem;
            border-bottom: 2px solid #e8f5e9;
            position: relative;
        }

        .header::after {
            content: '';
            position: absolute;
            bottom: -2px;
            left: 50%;
            transform: translateX(-50%);
            width: 100px;
            height: 2px;
            background-color: var(--primary-color);
        }

        h1 {
            font-size: 2.5rem;
            font-weight: 700;
            color: var(--primary-color);
            margin-bottom: 0.5rem;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
        }

        .subtitle {
            color: var(--text-secondary);
            font-size: 1.1rem;
            font-weight: 400;
        }

        h2 {
            font-size: 1.5rem;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 1rem;
            display: flex;
            align-items: center;
        }

        h2 i {
            margin-right: 0.75rem;
            color: var(--primary-color);
        }

        h3 {
            font-size: 1.25rem;
            font-weight: 500;
            color: var(--text-primary);
            margin: 1.5rem 0 0.75rem 0;
        }

        .upload-section {
            margin: 1.5rem 0;
            padding: 1.5rem;
            border: 2px dashed #a5d6a7;
            border-radius: var(--radius-md);
            background-color: #f1f8e9;
            transition: all var(--transition-speed) ease;
            position: relative;
            overflow: hidden;
        }

        .upload-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(45deg, rgba(46, 204, 113, 0.05) 0%, rgba(46, 204, 113, 0) 70%);
            z-index: 0;
        }

        .upload-section:hover {
            border-color: var(--primary-color);
            transform: translateY(-3px);
            box-shadow: 0 5px 15px rgba(46, 204, 113, 0.1);
        }

        .file-input-wrapper {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            padding: 1.5rem;
            text-align: center;
            position: relative;
            z-index: 1;
        }

        .file-input-wrapper i {
            font-size: 2.5rem;
            color: var(--primary-color);
            margin-bottom: 1rem;
            transition: transform 0.5s ease;
        }

        .upload-section:hover .file-input-wrapper i {
            transform: translateY(-5px);
        }

        .file-input-label {
            display: inline-block;
            padding: 0.75rem 1.5rem;
            background-color: var(--primary-color);
            color: white;
            border-radius: var(--radius-md);
            cursor: pointer;
            font-weight: 500;
            transition: all var(--transition-speed) ease;
            margin-bottom: 0.75rem;
            position: relative;
            overflow: hidden;
            box-shadow: 0 3px 6px rgba(0, 0, 0, 0.1);
        }

        .file-input-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: 0.5s;
        }

        .file-input-label:hover {
            background-color: var(--primary-hover);
            transform: translateY(-2px);
            box-shadow: 0 5px 10px rgba(0, 0, 0, 0.15);
        }

        .file-input-label:hover::after {
            left: 100%;
        }

        .file-input {
            position: absolute;
            width: 1px;
            height: 1px;
            padding: 0;
            margin: -1px;
            overflow: hidden;
            clip: rect(0, 0, 0, 0);
            border: 0;
        }

        .file-info {
            font-size: 0.875rem;
            color: var(--text-secondary);
        }

        .preview-container {
            display: flex;
            flex-wrap: wrap;
            gap: 1rem;
            margin-top: 1rem;
        }

        .text-section {
            margin: 1.5rem 0;
            padding: 1.5rem;
            border: 1px solid #c8e6c9;
            border-radius: var(--radius-md);
            background-color: var(--bg-white);
            box-shadow: var(--shadow-sm);
            transition: all var(--transition-speed) ease;
        }

        .text-section:hover {
            box-shadow: var(--shadow-md);
        }

        .results {
            margin-top: 1.5rem;
            padding: 1.5rem;
            background-color: var(--bg-white);
            border-radius: var(--radius-md);
            border: 1px solid #c8e6c9;
            box-shadow: var(--shadow-md);
            transition: all var(--transition-speed) ease;
        }

        .results:hover {
            box-shadow: 0 8px 16px rgba(46, 204, 113, 0.15);
        }

        .hidden {
            display: none;
        }

        textarea {
            width: 100%;
            min-height: 200px;
            margin-top: 0.75rem;
            padding: 1rem;
            border: 1px solid #c8e6c9;
            border-radius: var(--radius-md);
            font-family: 'Poppins', monospace;
            font-size: 0.9rem;
            resize: vertical;
            transition: border-color var(--transition-speed) ease, box-shadow var(--transition-speed) ease;
            background-color: #fafafa;
        }

        textarea:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(46, 204, 113, 0.15);
        }

        .button-container {
            display: flex;
            justify-content: center;
            margin: 1.5rem 0;
            gap: 1rem;
        }

        button {
            padding: 0.75rem 1.5rem;
            background-color: var(--primary-color);
            color: white;
            border: none;
            border-radius: var(--radius-md);
            cursor: pointer;
            font-size: 1rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all var(--transition-speed) ease;
            box-shadow: 0 3px 6px rgba(0, 0, 0, 0.1);
            position: relative;
            overflow: hidden;
        }

        button::after {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: 0.5s;
        }

        button:hover::after {
            left: 100%;
        }

        button i {
            margin-right: 0.5rem;
        }

        button:hover {
            background-color: var(--primary-hover);
            transform: translateY(-3px);
            box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
        }

        button:active {
            transform: translateY(-1px);
        }

        button:disabled {
            background-color: var(--text-secondary);
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }

        .loading {
            display: flex;
            align-items: center;
            margin-left: 1rem;
            font-size: 0.9rem;
            color: var(--text-secondary);
        }

        .loading i {
            margin-right: 0.5rem;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .progress-container {
            width: 100%;
            background-color: #e8f5e9;
            border-radius: 9999px;
            height: 0.5rem;
            margin: 1rem 0;
            overflow: hidden;
            box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.1);
        }

        .progress-bar {
            height: 100%;
            background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
            border-radius: 9999px;
            width: 0%;
            transition: width 0.5s ease;
            box-shadow: 0 0 5px rgba(46, 204, 113, 0.5);
        }

        /* Loading animation for assessment */
        .assessment-loading {
            position: relative;
            padding: 2rem;
            margin: 1.5rem 0;
            background-color: #f1f8e9;
            border-radius: var(--radius-md);
            text-align: center;
            border: 1px solid #c8e6c9;
            overflow: hidden;
        }

        .assessment-loading::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 5px;
            background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
            animation: loading-bar 2s infinite linear;
        }

        @keyframes loading-bar {
            0% { transform: translateX(-100%); }
            100% { transform: translateX(100%); }
        }

        .assessment-steps {
            display: flex;
            flex-direction: column;
            gap: 1rem;
            margin-top: 1.5rem;
        }

        .assessment-step {
            display: flex;
            align-items: center;
            padding: 0.75rem 1rem;
            background-color: var(--bg-white);
            border-radius: var(--radius-md);
            box-shadow: var(--shadow-sm);
            transition: all 0.3s ease;
            border-left: 3px solid #e0e0e0;
        }

        .assessment-step.active {
            border-left: 3px solid var(--primary-color);
            background-color: #f8fffa;
            box-shadow: var(--shadow-md);
        }

        .assessment-step.completed {
            border-left: 3px solid var(--success-color);
        }

        .step-icon {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 2rem;
            height: 2rem;
            border-radius: 50%;
            background-color: #f1f8e9;
            margin-right: 1rem;
            color: var(--text-secondary);
        }

        .assessment-step.active .step-icon {
            background-color: var(--primary-color);
            color: white;
            animation: pulse 1.5s infinite;
        }

        .assessment-step.completed .step-icon {
            background-color: var(--success-color);
            color: white;
        }

        @keyframes pulse {
            0% { box-shadow: 0 0 0 0 rgba(46, 204, 113, 0.4); }
            70% { box-shadow: 0 0 0 10px rgba(46, 204, 113, 0); }
            100% { box-shadow: 0 0 0 0 rgba(46, 204, 113, 0); }
        }

        .step-text {
            font-weight: 500;
        }

        .assessment-step.active .step-text {
            color: var(--primary-color);
        }

        .assessment-step.completed .step-text {
            color: var(--success-color);
        }

        .step-status {
            margin-left: auto;
            font-size: 0.875rem;
            color: var(--text-secondary);
        }

        .assessment-step.active .step-status {
            color: var(--primary-color);
        }

        .assessment-step.completed .step-status {
            color: var(--success-color);
        }

        .grading-result {
            padding: 1.5rem;
            background-color: #f1f8e9;
            border-radius: var(--radius-md);
            margin-top: 1rem;
            border: 1px solid #c8e6c9;
            transition: all var(--transition-speed) ease;
        }

        .grading-result:hover {
            box-shadow: 0 5px 15px rgba(46, 204, 113, 0.1);
        }

        .grading-result h4 {
            font-size: 1.1rem;
            font-weight: 600;
            margin-bottom: 0.75rem;
            color: var(--text-primary);
        }

        .score-container {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 1rem;
            padding: 1rem;
            background-color: var(--bg-white);
            border-radius: var(--radius-md);
            box-shadow: var(--shadow-sm);
            border: 1px solid #c8e6c9;
            transition: all var(--transition-speed) ease;
        }

        .score-container:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 10px rgba(46, 204, 113, 0.1);
        }

        .score-label {
            font-weight: 500;
        }

        .score-value {
            font-size: 1.25rem;
            font-weight: 700;
            color: var(--primary-color);
            display: flex;
            align-items: center;
        }

        .score-value::before {
            content: '\f058';
            font-family: 'Font Awesome 5 Free';
            margin-right: 0.5rem;
            font-size: 1.2rem;
        }

        /* Fluid animations */
        @keyframes float {
            0% { transform: translateY(0px); }
            50% { transform: translateY(-5px); }
            100% { transform: translateY(0px); }
        }

        .file-input-wrapper i {
            animation: float 3s ease-in-out infinite;
        }

        /* Responsive adjustments */
        @media (max-width: 768px) {
            body {
                padding: 1rem;
            }
            
            .container {
                padding: 1.5rem;
            }
            
            h1 {
                font-size: 1.75rem;
            }
            
            .button-container {
                flex-direction: column;
            }
            
            button {
                width: 100%;
            }
        }

        /* New Student Button */
        .new-student-container {
            text-align: center;
            margin-bottom: 2rem;
            display: none; /* Hidden by default */
            animation: fadeIn 0.5s ease-in-out;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(-10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .new-student-btn {
            padding: 0.75rem 2rem;
            background-color: var(--accent-color);
            color: white;
            border: none;
            border-radius: var(--radius-md);
            cursor: pointer;
            font-size: 1rem;
            font-weight: 500;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            transition: all var(--transition-speed) ease;
            box-shadow: 0 3px 6px rgba(0, 0, 0, 0.1);
            position: relative;
            overflow: hidden;
        }

        .new-student-btn::after {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: 0.5s;
        }

        .new-student-btn:hover::after {
            left: 100%;
        }

        .new-student-btn i {
            margin-right: 0.5rem;
        }

        .new-student-btn:hover {
            background-color: #2980b9;
            transform: translateY(-3px);
            box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
        }

        .new-student-btn:active {
            transform: translateY(-1px);
        }

        /* Footer Styles */
        .footer {
            margin-top: 2rem;
            padding: 1.5rem;
            background-color: #f1f8e9;
            border-radius: var(--radius-lg);
            box-shadow: var(--shadow-md);
            text-align: center;
            border-top: 2px solid #c8e6c9;
            position: relative;
            overflow: hidden;
            max-width: 1100px;
            margin-left: auto;
            margin-right: auto;
            margin-bottom: 2rem;
        }

        .footer::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(45deg, rgba(46, 204, 113, 0.05) 0%, rgba(46, 204, 113, 0) 70%);
            z-index: 0;
        }

        .footer-content {
            position: relative;
            z-index: 1;
        }

        .footer-logo {
            font-weight: 700;
            font-size: 1.2rem;
            color: var(--primary-color);
            margin-bottom: 0.5rem;
            display: inline-block;
        }

        .footer-logo i {
            margin-right: 0.5rem;
        }

        .footer-text {
            color: var(--text-secondary);
            font-size: 0.9rem;
            margin-bottom: 0.75rem;
        }

        .footer-link {
            color: var(--accent-color);
            text-decoration: none;
            transition: color 0.3s ease;
            font-weight: 500;
        }

        .footer-link:hover {
            color: var(--primary-color);
            text-decoration: underline;
        }

        .footer-version {
            display: inline-block;
            padding: 0.25rem 0.75rem;
            background-color: rgba(46, 204, 113, 0.1);
            border-radius: 9999px;
            font-size: 0.8rem;
            color: var(--primary-color);
            margin-top: 0.75rem;
        }

        .footer-divider {
            width: 50px;
            height: 2px;
            background-color: var(--primary-color);
            margin: 0.75rem auto;
            opacity: 0.5;
        }

        @media (max-width: 768px) {
            .footer {
                padding: 1rem;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Smart Test Grading System</h1>
            <p class="subtitle">Upload test papers and answer sheets for automated grading and assessment</p>
        </div>
        
        <!-- New Student Button Container -->
        <div id="newStudentContainer" class="new-student-container">
            <button id="newStudentBtn" class="new-student-btn"><i class="fas fa-user-plus"></i>Grade Another Student</button>
        </div>
        
        <div class="upload-section">
            <h2><i class="fas fa-file-alt"></i>Upload Test Papers</h2>
            <div class="file-input-wrapper">
                <i class="fas fa-cloud-upload-alt"></i>
                <label for="testPapers" class="file-input-label">Choose Test Papers</label>
                <input type="file" id="testPapers" class="file-input" multiple accept="image/*,application/pdf" capture>
                <p class="file-info">Supported formats: JPG, PNG, PDF (Multiple files allowed)</p>
            </div>
            <div id="testPaperPreview" class="preview-container"></div>
        </div>

        <div class="upload-section">
            <h2><i class="fas fa-check-square"></i>Upload Answer Sheets</h2>
            <div class="file-input-wrapper">
                <i class="fas fa-cloud-upload-alt"></i>
                <label for="answerSheets" class="file-input-label">Choose Answer Sheets</label>
                <input type="file" id="answerSheets" class="file-input" multiple accept="image/*,application/pdf" capture>
                <p class="file-info">Supported formats: JPG, PNG, PDF (Multiple files allowed)</p>
            </div>
            <div id="answerSheetPreview" class="preview-container"></div>
        </div>

        <div class="button-container">
            <button id="processBtn"><i class="fas fa-cogs"></i>Process Files</button>
        </div>
        
        <div id="processingContainer" class="hidden">
            <div class="progress-container">
                <div id="progressBar" class="progress-bar"></div>
            </div>
            <span id="processingStatus" class="loading"><i class="fas fa-spinner"></i>Processing...</span>
        </div>

        <div id="textExtraction" class="text-section hidden">
            <h2><i class="fas fa-file-text"></i>Extracted Content</h2>
            
            <div>
                <h3>Test Paper Content</h3>
                <textarea id="testContent" placeholder="Extracted test content will appear here..."></textarea>
            </div>
            
            <div>
                <h3>Answer Sheet Content</h3>
                <textarea id="answerContent" placeholder="Extracted answer content will appear here..."></textarea>
            </div>
            
            <div class="button-container">
                <button id="assessBtn" class="hidden"><i class="fas fa-chart-bar"></i>Assess and Grade</button>
            </div>
        </div>

        <!-- Assessment loading section -->
        <div id="assessmentLoading" class="assessment-loading hidden">
            <h3><i class="fas fa-brain"></i> AI Assessment in Progress</h3>
            <p>Please wait while our AI analyzes and grades the test papers...</p>
            
            <div class="assessment-steps">
                <div id="step1" class="assessment-step">
                    <div class="step-icon"><i class="fas fa-file-alt"></i></div>
                    <div class="step-text">Analyzing test content</div>
                    <div class="step-status">Waiting...</div>
                </div>
                <div id="step2" class="assessment-step">
                    <div class="step-icon"><i class="fas fa-check-square"></i></div>
                    <div class="step-text">Comparing with answer key</div>
                    <div class="step-status">Waiting...</div>
                </div>
                <div id="step3" class="assessment-step">
                    <div class="step-icon"><i class="fas fa-calculator"></i></div>
                    <div class="step-text">Calculating scores</div>
                    <div class="step-status">Waiting...</div>
                </div>
                <div id="step4" class="assessment-step">
                    <div class="step-icon"><i class="fas fa-comment-alt"></i></div>
                    <div class="step-text">Generating feedback</div>
                    <div class="step-status">Waiting...</div>
                </div>
            </div>
        </div>

        <div id="results" class="results hidden">
            <h2><i class="fas fa-award"></i>Grading Results</h2>
            <div id="gradingOutput" class="grading-result"></div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="footer">
        <div class="footer-content">
            <div class="footer-logo">
                <i class="fas fa-graduation-cap"></i>SMAS @Web
            </div>
            <div class="footer-divider"></div>
            <p class="footer-text">
                Developed by <a href="https://www.dakoiims.com" target="_blank" class="footer-link">Dakoii Systems</a>
            </p>
            <p class="footer-text">
                Copyright &copy; 2025 All Rights Reserved
            </p>
            <span class="footer-version">Beta Version</span>
        </div>
    </footer>

    <script>
        // Initialize Google Gemini 2.0 API
        const API_KEY = 'AIzaSyApNBtEwylsW_q5zUOvIDP3pj87WDShSLA';

        // Check for low memory conditions on mobile devices
        function checkMemoryCondition() {
            // Only works on Chrome-based browsers that support performance.memory
            if (navigator.userAgent.toLowerCase().indexOf('android') > -1 && 
                window.performance && 
                window.performance.memory) {
                
                const memoryInfo = window.performance.memory;
                const memoryUsageRatio = memoryInfo.usedJSHeapSize / memoryInfo.jsHeapSizeLimit;
                
                // If memory usage is over 70%, show warning
                if (memoryUsageRatio > 0.7) {
                    console.warn('Memory usage is high: ' + Math.round(memoryUsageRatio * 100) + '%');
                    
                    // Show warning to user
                    const warningElement = document.createElement('div');
                    warningElement.innerHTML = `
                        <div style="padding: 0.75rem; margin: 1rem 0; background-color: #fff3cd; color: #856404; 
                                    border-radius: var(--radius-md); border: 1px solid #ffeeba;">
                            <i class="fas fa-exclamation-triangle" style="margin-right: 0.5rem;"></i>
                            <strong>Warning:</strong> Your device is running low on memory. 
                            Consider closing other apps or browser tabs, or try processing fewer files.
                        </div>
                    `;
                    
                    // Insert at the top of the container
                    const container = document.querySelector('.container');
                    container.insertBefore(warningElement, container.firstChild);
                    
                    return true;
                }
            }
            return false;
        }

        // File storage in memory
        const fileStorage = {
            testPapers: [],
            answerSheets: []
        };

        // Get DOM elements
        const processBtn = document.getElementById('processBtn');
        const assessBtn = document.getElementById('assessBtn');
        const processingStatus = document.getElementById('processingStatus');
        const processingContainer = document.getElementById('processingContainer');
        const progressBar = document.getElementById('progressBar');
        const textExtraction = document.getElementById('textExtraction');
        const testContentArea = document.getElementById('testContent');
        const answerContentArea = document.getElementById('answerContent');
        const results = document.getElementById('results');
        const gradingOutput = document.getElementById('gradingOutput');
        const testPapersInput = document.getElementById('testPapers');
        const answerSheetsInput = document.getElementById('answerSheets');
        const testPaperPreview = document.getElementById('testPaperPreview');
        const answerSheetPreview = document.getElementById('answerSheetPreview');
        const assessmentLoading = document.getElementById('assessmentLoading');
        const assessmentSteps = {
            step1: document.getElementById('step1'),
            step2: document.getElementById('step2'),
            step3: document.getElementById('step3'),
            step4: document.getElementById('step4')
        };
        const newStudentBtn = document.getElementById('newStudentBtn');

        // Add event listeners
        processBtn.addEventListener('click', processFiles);
        assessBtn.addEventListener('click', assessAndGrade);
        testPapersInput.addEventListener('change', (event) => handleFileSelection(event, 'testPapers'));
        answerSheetsInput.addEventListener('change', (event) => handleFileSelection(event, 'answerSheets'));
        newStudentBtn.addEventListener('click', resetPage);

        // Handle file selection and store in memory
        async function handleFileSelection(event, fileType) {
            const files = event.target.files;
            const previewContainer = fileType === 'testPapers' ? testPaperPreview : answerSheetPreview;
            
            // Check memory condition on mobile
            checkMemoryCondition();
            
            // Clear previous files from storage
            fileStorage[fileType] = [];
            
            // Clear preview container
            previewContainer.innerHTML = '';
            
            if (files.length > 0) {
                // Show loading indicator in preview
                const loadingElement = document.createElement('div');
                loadingElement.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Loading files into memory...';
                loadingElement.style.padding = '0.5rem';
                loadingElement.style.color = 'var(--text-secondary)';
                previewContainer.appendChild(loadingElement);
                
                try {
                    // Check if running on mobile device
                    const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
                    const maxFileSize = isMobile ? 5 * 1024 * 1024 : 10 * 1024 * 1024; // 5MB for mobile, 10MB for desktop
                    const maxFiles = isMobile ? 3 : 10; // Limit number of files on mobile
                    
                    // Warn if too many files selected on mobile
                    if (isMobile && files.length > maxFiles) {
                        console.warn(`Mobile device detected. Processing only the first ${maxFiles} files to avoid memory issues.`);
                        // Add warning to UI
                        const warningElement = document.createElement('div');
                        warningElement.innerHTML = `<i class="fas fa-exclamation-triangle"></i> Mobile device detected. Only the first ${maxFiles} files will be processed to avoid memory issues.`;
                        warningElement.style.color = 'var(--warning-color)';
                        warningElement.style.padding = '0.5rem';
                        warningElement.style.marginBottom = '0.5rem';
                        previewContainer.appendChild(warningElement);
                    }
                    
                    // Process each file and store in memory
                    const filesToProcess = isMobile ? Math.min(files.length, maxFiles) : files.length;
                    
                    for (let i = 0; i < filesToProcess; i++) {
                        const file = files[i];
                        
                        // Check file size - limit based on device
                        if (file.size > maxFileSize) {
                            throw new Error(`File "${file.name}" exceeds the ${isMobile ? '5MB' : '10MB'} size limit. Please use a smaller file.`);
                        }
                        
                        // Store file metadata and content in memory
                        const fileObject = {
                            name: file.name,
                            type: file.type,
                            size: file.size,
                            lastModified: file.lastModified,
                            file: file, // Store the original File object
                            loaded: false, // Flag to track if file is loaded in memory
                            base64Data: null, // Will store base64 data once loaded
                            rawData: null, // Will store raw data for PDF conversion
                            isPdf: file.type === 'application/pdf', // Flag to track if file is a PDF
                            pageImages: [] // Will store page images for PDF files
                        };
                        
                        // Add to storage
                        fileStorage[fileType].push(fileObject);
                    }
                    
                    // Load files into memory (in background)
                    loadFilesIntoMemory(fileType).then(() => {
                        // Update UI after files are loaded
                        updateFilePreview(fileType);
                    }).catch(error => {
                        console.error('Error loading files into memory:', error);
                        alert(`Error loading files: ${error.message || 'Unknown error'}`);
                        // Clear the file input
                        event.target.value = '';
                        // Clear storage for this type
                        fileStorage[fileType] = [];
                        // Update preview to show error
                        previewContainer.innerHTML = `
                            <div style="color: var(--danger-color); padding: 0.5rem;">
                                <i class="fas fa-exclamation-circle"></i> 
                                Error loading files: ${error.message || 'Unknown error'}
                            </div>
                        `;
                    });
                } catch (error) {
                    console.error('Error handling file selection:', error);
                    alert(`Error selecting files: ${error.message || 'Unknown error'}`);
                    // Clear the file input
                    event.target.value = '';
                    // Clear storage for this type
                    fileStorage[fileType] = [];
                    // Update preview to show error
                    previewContainer.innerHTML = `
                        <div style="color: var(--danger-color); padding: 0.5rem;">
                            <i class="fas fa-exclamation-circle"></i> 
                            Error: ${error.message || 'Unknown error'}
                        </div>
                    `;
                }
            }
        }
        
        // Load all files of a type into memory
        async function loadFilesIntoMemory(fileType) {
            const files = fileStorage[fileType];
            
            if (files.length === 0) {
                return;
            }
            
            // Check if running on mobile device
            const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
            
            // Process files in batches to avoid memory issues
            const batchSize = isMobile ? 1 : 3; // Process one file at a time on mobile
            const batches = Math.ceil(files.length / batchSize);
            
            for (let batch = 0; batch < batches; batch++) {
                const start = batch * batchSize;
                const end = Math.min(start + batchSize, files.length);
                const batchPromises = [];
                
                // Create promises for each file in the batch
                for (let i = start; i < end; i++) {
                    batchPromises.push(loadFileIntoMemory(files[i]));
                }
                
                try {
                    // Wait for all files in this batch to load
                    await Promise.all(batchPromises);
                    
                    // On mobile, add a small delay between batches to allow garbage collection
                    if (isMobile && batch < batches - 1) {
                        await new Promise(resolve => setTimeout(resolve, 300));
                    }
                } catch (error) {
                    console.error(`Error loading batch ${batch + 1}/${batches}:`, error);
                    // Continue with next batch even if this one failed
                }
            }
            
            // Verify all files are loaded
            const unloadedFiles = files.filter(file => !file.loaded);
            if (unloadedFiles.length > 0) {
                const fileNames = unloadedFiles.map(f => f.name).join(', ');
                throw new Error(`Failed to load ${unloadedFiles.length} files: ${fileNames}`);
            }
        }
        
        // Load a single file into memory
        function loadFileIntoMemory(fileObject) {
            return new Promise((resolve, reject) => {
                try {
                    const reader = new FileReader();
                    
                    // Add timeout handling for mobile devices
                    const timeoutId = setTimeout(() => {
                        reader.abort();
                        reject(new Error(`Timeout reading file "${fileObject.name}". Please try again with a smaller file.`));
                    }, 30000); // 30 second timeout
                    
                    reader.onload = () => {
                        try {
                            clearTimeout(timeoutId); // Clear timeout on successful load
                            
                            if (!reader.result) {
                                reject(new Error(`Failed to read file "${fileObject.name}"`));
                                return;
                            }
                            
                            // For PDF files, we'll store the raw data and convert to images later
                            if (fileObject.type === 'application/pdf') {
                                fileObject.rawData = reader.result;
                                fileObject.loaded = true;
                                fileObject.isPdf = true;
                                resolve(fileObject);
                                return;
                            }
                            
                            // For image files, extract the base64 data as before
                            const base64String = reader.result
                                .toString()
                                .replace('data:', '')
                                .replace(/^.+,/, '');
                                
                            if (!base64String) {
                                reject(new Error(`Failed to extract base64 data from file "${fileObject.name}"`));
                                return;
                            }
                            
                            // Store the base64 data in the file object
                            fileObject.base64Data = base64String;
                            fileObject.loaded = true;
                            fileObject.isPdf = false;
                            
                            // Force garbage collection on mobile devices by nullifying large objects
                            setTimeout(() => {
                                // This helps free memory on mobile devices
                                reader.onload = null;
                                reader.onerror = null;
                                reader.onabort = null;
                            }, 100);
                            
                            resolve(fileObject);
                        } catch (error) {
                            clearTimeout(timeoutId); // Clear timeout on error
                            console.error(`Error processing file "${fileObject.name}":`, error);
                            reject(new Error(`Error processing file "${fileObject.name}": ${error.message || 'Unknown error'}`));
                        }
                    };
                    
                    reader.onerror = (event) => {
                        clearTimeout(timeoutId); // Clear timeout on error
                        console.error(`FileReader error for "${fileObject.name}":`, event);
                        reject(new Error(`Error reading file "${fileObject.name}": ${event.target.error?.message || 'Unknown error'}`));
                    };
                    
                    reader.onabort = () => {
                        clearTimeout(timeoutId); // Clear timeout on abort
                        reject(new Error(`File reading was aborted for "${fileObject.name}"`));
                    };
                    
                    // Start reading the file
                    if (fileObject.type === 'application/pdf') {
                        reader.readAsArrayBuffer(fileObject.file);
                    } else {
                        reader.readAsDataURL(fileObject.file);
                    }
                } catch (error) {
                    console.error(`Error setting up FileReader for "${fileObject.name}":`, error);
                    reject(new Error(`Error preparing to read file "${fileObject.name}": ${error.message || 'Unknown error'}`));
                }
            });
        }

        // Update file preview when files are loaded into memory
        function updateFilePreview(fileType) {
            const files = fileStorage[fileType];
            const previewContainer = fileType === 'testPapers' ? testPaperPreview : answerSheetPreview;
            
            previewContainer.innerHTML = '';
            
            if (files.length > 0) {
                // Add file count text
                const fileCountText = document.createElement('p');
                fileCountText.textContent = `${files.length} file(s) loaded into memory`;
                fileCountText.style.fontWeight = '500';
                fileCountText.style.marginTop = '0.5rem';
                fileCountText.style.color = 'var(--success-color)';
                previewContainer.appendChild(fileCountText);
                
                // Count PDF files
                const pdfCount = files.filter(file => file.isPdf).length;
                if (pdfCount > 0) {
                    const pdfInfoText = document.createElement('p');
                    pdfInfoText.textContent = `${pdfCount} PDF file(s) will be converted to images for better text extraction`;
                    pdfInfoText.style.fontSize = '0.875rem';
                    pdfInfoText.style.color = 'var(--accent-color)';
                    pdfInfoText.style.marginTop = '0.25rem';
                    previewContainer.appendChild(pdfInfoText);
                }
                
                // Display file names with status indicators
                for (let i = 0; i < Math.min(files.length, 5); i++) {
                    const file = files[i];
                    const fileItem = document.createElement('div');
                    fileItem.style.display = 'flex';
                    fileItem.style.alignItems = 'center';
                    fileItem.style.padding = '0.5rem';
                    fileItem.style.backgroundColor = 'var(--bg-white)';
                    fileItem.style.borderRadius = 'var(--radius-sm)';
                    fileItem.style.boxShadow = 'var(--shadow-sm)';
                    fileItem.style.transition = 'all 0.3s ease';
                    fileItem.style.borderLeft = file.loaded ? 
                        '3px solid var(--success-color)' : 
                        '3px solid var(--warning-color)';
                    
                    fileItem.addEventListener('mouseover', () => {
                        fileItem.style.transform = 'translateX(5px)';
                        fileItem.style.boxShadow = 'var(--shadow-md)';
                    });
                    
                    fileItem.addEventListener('mouseout', () => {
                        fileItem.style.transform = 'translateX(0)';
                        fileItem.style.boxShadow = 'var(--shadow-sm)';
                    });
                    
                    // Icon based on file status and type
                    const icon = document.createElement('i');
                    if (!file.loaded) {
                        icon.className = 'fas fa-exclamation-circle';
                        icon.style.color = 'var(--warning-color)';
                    } else if (file.isPdf) {
                        icon.className = 'fas fa-file-pdf';
                        icon.style.color = 'var(--accent-color)';
                    } else if (file.type.startsWith('image/')) {
                        icon.className = 'fas fa-file-image';
                        icon.style.color = 'var(--success-color)';
                    } else {
                        icon.className = 'fas fa-check-circle';
                        icon.style.color = 'var(--success-color)';
                    }
                    icon.style.marginRight = '0.5rem';
                    
                    // File name
                    const fileName = document.createElement('span');
                    fileName.textContent = file.name;
                    fileName.style.fontSize = '0.875rem';
                    
                    // File type badge for PDFs
                    let fileTypeBadge = null;
                    if (file.isPdf) {
                        fileTypeBadge = document.createElement('span');
                        fileTypeBadge.textContent = 'PDF';
                        fileTypeBadge.style.fontSize = '0.7rem';
                        fileTypeBadge.style.padding = '0.1rem 0.4rem';
                        fileTypeBadge.style.backgroundColor = 'var(--accent-color)';
                        fileTypeBadge.style.color = 'white';
                        fileTypeBadge.style.borderRadius = '9999px';
                        fileTypeBadge.style.marginLeft = '0.5rem';
                    }
                    
                    // File size
                    const fileSize = document.createElement('span');
                    fileSize.textContent = formatFileSize(file.size);
                    fileSize.style.fontSize = '0.75rem';
                    fileSize.style.color = 'var(--text-secondary)';
                    fileSize.style.marginLeft = 'auto';
                    
                    fileItem.appendChild(icon);
                    fileItem.appendChild(fileName);
                    if (fileTypeBadge) {
                        fileItem.appendChild(fileTypeBadge);
                    }
                    fileItem.appendChild(fileSize);
                    previewContainer.appendChild(fileItem);
                }
                
                // Show if there are more files
                if (files.length > 5) {
                    const moreFiles = document.createElement('p');
                    moreFiles.textContent = `+ ${files.length - 5} more files`;
                    moreFiles.style.fontSize = '0.875rem';
                    moreFiles.style.color = 'var(--text-secondary)';
                    moreFiles.style.marginTop = '0.5rem';
                    previewContainer.appendChild(moreFiles);
                }
                
                // Add memory usage information
                const memoryInfo = document.createElement('p');
                const totalSize = files.reduce((total, file) => total + file.size, 0);
                memoryInfo.textContent = `Total memory usage: ${formatFileSize(totalSize)}`;
                memoryInfo.style.fontSize = '0.75rem';
                memoryInfo.style.color = 'var(--text-secondary)';
                memoryInfo.style.marginTop = '0.5rem';
                previewContainer.appendChild(memoryInfo);
            }
        }
        
        // Format file size in human-readable format
        function formatFileSize(bytes) {
            if (bytes === 0) return '0 Bytes';
            
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }

        async function processFiles() {
            // Check memory condition on mobile
            const isLowMemory = checkMemoryCondition();
            
            // Verify files are loaded in memory
            if (fileStorage.testPapers.length === 0 || fileStorage.answerSheets.length === 0) {
                alert('Please upload both test papers and answer sheets');
                return;
            }
            
            // Check if all files are loaded into memory
            const unloadedTestPapers = fileStorage.testPapers.filter(file => !file.loaded);
            const unloadedAnswerSheets = fileStorage.answerSheets.filter(file => !file.loaded);
            
            // Show processing status and disable button
            processingContainer.classList.remove('hidden');
            processBtn.disabled = true;

            try {
                // If there are unloaded files, show warning but continue with loaded files
                if (unloadedTestPapers.length > 0 || unloadedAnswerSheets.length > 0) {
                    const warningMessage = [
                        'Some files have not been fully loaded into memory:',
                        unloadedTestPapers.length > 0 ? `- ${unloadedTestPapers.length} test papers` : '',
                        unloadedAnswerSheets.length > 0 ? `- ${unloadedAnswerSheets.length} answer sheets` : '',
                        'Only loaded files will be processed.'
                    ].filter(Boolean).join('\n');
                    
                    console.warn(warningMessage);
                    
                    // Create a warning element to display in the UI
                    const warningElement = document.createElement('div');
                    warningElement.style.padding = '0.75rem';
                    warningElement.style.marginBottom = '1rem';
                    warningElement.style.backgroundColor = '#fff3cd';
                    warningElement.style.color = '#856404';
                    warningElement.style.borderRadius = 'var(--radius-md)';
                    warningElement.style.border = '1px solid #ffeeba';
                    warningElement.innerHTML = `
                        <i class="fas fa-exclamation-triangle" style="margin-right: 0.5rem;"></i>
                        <strong>Warning:</strong> Some files could not be loaded. Only loaded files will be processed.
                    `;
                    
                    // Filter out unloaded files
                    fileStorage.testPapers = fileStorage.testPapers.filter(file => file.loaded);
                    fileStorage.answerSheets = fileStorage.answerSheets.filter(file => file.loaded);
                    
                    // If no files are loaded after filtering, show error and return
                    if (fileStorage.testPapers.length === 0 || fileStorage.answerSheets.length === 0) {
                        textExtraction.classList.remove('hidden');
                        testContentArea.value = 'Error: Not enough valid files were loaded to process. Please upload files again.';
                        answerContentArea.value = 'Error: Not enough valid files were loaded to process. Please upload files again.';
                        
                        // Insert warning at the top of text extraction section
                        textExtraction.insertBefore(warningElement, textExtraction.firstChild);
                        
                        // Hide assess button since we don't have enough valid data
                        assessBtn.classList.add('hidden');
                        
                        // Scroll to text extraction section
                        textExtraction.scrollIntoView({ behavior: 'smooth' });
                        
                        throw new Error('Not enough valid files were loaded to process');
                    }
                    
                    // Insert warning at the top of text extraction section
                    textExtraction.insertBefore(warningElement, textExtraction.firstChild);
                }

                // Check if running on mobile device with low memory
                const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
                if (isMobile && isLowMemory) {
                    // Limit the number of files to process on low memory mobile devices
                    const maxFilesToProcess = 2;
                    if (fileStorage.testPapers.length > maxFilesToProcess) {
                        console.warn(`Low memory on mobile device. Limiting to ${maxFilesToProcess} test papers.`);
                        fileStorage.testPapers = fileStorage.testPapers.slice(0, maxFilesToProcess);
                    }
                    if (fileStorage.answerSheets.length > maxFilesToProcess) {
                        console.warn(`Low memory on mobile device. Limiting to ${maxFilesToProcess} answer sheets.`);
                        fileStorage.answerSheets = fileStorage.answerSheets.slice(0, maxFilesToProcess);
                    }
                }

                // Process test papers from memory
                const testContent = await extractTextFromMemoryFiles(fileStorage.testPapers, 
                    "Please analyze this test paper image and extract all the content including questions and any student answers. Format the output in a clear, structured way.");
                
                // Process answer sheets from memory
                const answerContent = await extractTextFromMemoryFiles(fileStorage.answerSheets, 
                    "Please analyze this answer sheet image and extract all the correct answers in a structured format. List each question number and its corresponding answer.");
                
                // Populate text areas
                testContentArea.value = testContent;
                answerContentArea.value = answerContent;
                
                // Show text extraction section
                textExtraction.classList.remove('hidden');
                
                // Only show assess button if we have meaningful content
                if (testContent && answerContent && 
                    !testContent.includes('No text could be extracted') && 
                    !answerContent.includes('No text could be extracted')) {
                    assessBtn.classList.remove('hidden');
                } else {
                    assessBtn.classList.add('hidden');
                    
                    // Add error message if extraction failed
                    const errorElement = document.createElement('div');
                    errorElement.style.padding = '0.75rem';
                    errorElement.style.marginTop = '1rem';
                    errorElement.style.backgroundColor = '#f8d7da';
                    errorElement.style.color = '#721c24';
                    errorElement.style.borderRadius = 'var(--radius-md)';
                    errorElement.style.border = '1px solid #f5c6cb';
                    errorElement.innerHTML = `
                        <i class="fas fa-exclamation-circle" style="margin-right: 0.5rem;"></i>
                        <strong>Error:</strong> Could not extract meaningful text from the uploaded files. 
                        Please try again with clearer images.
                    `;
                    textExtraction.appendChild(errorElement);
                }
                
                // Scroll to text extraction section
                textExtraction.scrollIntoView({ behavior: 'smooth' });
            } catch (error) {
                console.error('Error details:', error);
                
                // Show text extraction section with error
                textExtraction.classList.remove('hidden');
                testContentArea.value = `Error processing files: ${error.message || 'Unknown error occurred. Please try again.'}`;
                answerContentArea.value = 'Processing failed. Please check the error message above and try again.';
                
                // Hide assess button since processing failed
                assessBtn.classList.add('hidden');
                
                // Add error notification
                const errorElement = document.createElement('div');
                errorElement.style.padding = '0.75rem';
                errorElement.style.marginTop = '1rem';
                errorElement.style.backgroundColor = '#f8d7da';
                errorElement.style.color = '#721c24';
                errorElement.style.borderRadius = 'var(--radius-md)';
                errorElement.style.border = '1px solid #f5c6cb';
                errorElement.innerHTML = `
                    <i class="fas fa-exclamation-circle" style="margin-right: 0.5rem;"></i>
                    <strong>Error:</strong> ${error.message || 'Unknown error occurred. Please try again.'}
                `;
                textExtraction.appendChild(errorElement);
                
                // Scroll to text extraction section
                textExtraction.scrollIntoView({ behavior: 'smooth' });
            } finally {
                // Hide processing status and enable button
                processingContainer.classList.add('hidden');
                processBtn.disabled = false;
                // Reset progress bar
                progressBar.style.width = '0%';
            }
        }

        // Convert PDF to images
        async function convertPdfToImages(fileObject) {
            try {
                if (!fileObject.isPdf || !fileObject.rawData) {
                    return fileObject; // Not a PDF or no raw data, return as is
                }
                
                console.log(`Converting PDF to images: ${fileObject.name}`);
                
                // Load the PDF document
                const loadingTask = pdfjsLib.getDocument({data: new Uint8Array(fileObject.rawData)});
                const pdf = await loadingTask.promise;
                
                console.log(`PDF loaded: ${fileObject.name}, pages: ${pdf.numPages}`);
                
                // Create an array to store images from each page
                const pageImages = [];
                
                // Process each page (up to 10 pages to avoid memory issues)
                const maxPages = Math.min(pdf.numPages, 10);
                
                for (let pageNum = 1; pageNum <= maxPages; pageNum++) {
                    try {
                        // Get the page
                        const page = await pdf.getPage(pageNum);
                        
                        // Set scale for rendering (higher scale = better quality but more memory)
                        // Increased scale for better text recognition
                        const scale = 2.0;
                        const viewport = page.getViewport({ scale });
                        
                        // Create a canvas for rendering
                        const canvas = document.createElement('canvas');
                        const context = canvas.getContext('2d', { alpha: false });
                        canvas.height = viewport.height;
                        canvas.width = viewport.width;
                        
                        // Set white background for better contrast
                        context.fillStyle = 'white';
                        context.fillRect(0, 0, canvas.width, canvas.height);
                        
                        // Render the page to canvas with high quality settings
                        const renderContext = {
                            canvasContext: context,
                            viewport: viewport,
                            renderInteractiveForms: true,
                            enableWebGL: true
                        };
                        
                        await page.render(renderContext).promise;
                        
                        // Apply image processing to enhance text visibility
                        try {
                            // Increase contrast slightly
                            context.globalCompositeOperation = 'source-over';
                            context.globalAlpha = 0.1;
                            context.fillStyle = 'black';
                            context.fillRect(0, 0, canvas.width, canvas.height);
                            context.globalAlpha = 1.0;
                        } catch (enhanceError) {
                            console.warn('Error enhancing image, continuing with original:', enhanceError);
                        }
                        
                        // Get image data from canvas with higher quality
                        const imageData = canvas.toDataURL('image/jpeg', 0.95);
                        const base64Data = imageData.replace('data:image/jpeg;base64,', '');
                        
                        // Add to page images array
                        pageImages.push({
                            pageNum,
                            base64Data
                        });
                        
                        // Log success for debugging
                        console.log(`Successfully converted page ${pageNum} of ${fileObject.name}`);
                        
                        // Clean up to free memory
                        canvas.width = 0;
                        canvas.height = 0;
                        
                    } catch (pageError) {
                        console.error(`Error processing page ${pageNum} of PDF ${fileObject.name}:`, pageError);
                        // Try alternative rendering method if the first one fails
                        try {
                            console.log(`Attempting alternative rendering for page ${pageNum}`);
                            const page = await pdf.getPage(pageNum);
                            const scale = 1.5;
                            const viewport = page.getViewport({ scale });
                            
                            const canvas = document.createElement('canvas');
                            const context = canvas.getContext('2d');
                            canvas.height = viewport.height;
                            canvas.width = viewport.width;
                            
                            // Fill with white background
                            context.fillStyle = 'white';
                            context.fillRect(0, 0, canvas.width, canvas.height);
                            
                            // Simplified rendering
                            await page.render({
                                canvasContext: context,
                                viewport: viewport
                            }).promise;
                            
                            const imageData = canvas.toDataURL('image/jpeg', 0.9);
                            const base64Data = imageData.replace('data:image/jpeg;base64,', '');
                            
                            pageImages.push({
                                pageNum,
                                base64Data
                            });
                            
                            console.log(`Successfully converted page ${pageNum} using alternative method`);
                            
                            // Clean up
                            canvas.width = 0;
                            canvas.height = 0;
                        } catch (altError) {
                            console.error(`Alternative rendering also failed for page ${pageNum}:`, altError);
                        }
                    }
                    
                    // Force garbage collection between pages
                    if (window.gc) {
                        window.gc();
                    } else {
                        // Manual cleanup
                        const memoryHog = [];
                        for (let i = 0; i < 10000; i++) {
                            memoryHog.push(new Array(10000));
                        }
                        memoryHog.length = 0;
                    }
                }
                
                console.log(`PDF conversion complete: ${fileObject.name}, converted ${pageImages.length} pages`);
                
                // Store the page images in the file object
                fileObject.pageImages = pageImages;
                
                // Clean up the raw data to free memory
                fileObject.rawData = null;
                
                return fileObject;
            } catch (error) {
                console.error(`Error converting PDF to images: ${fileObject.name}`, error);
                // If conversion fails, mark the file as not loaded
                fileObject.loaded = false;
                fileObject.error = `Failed to convert PDF: ${error.message || 'Unknown error'}`;
                return fileObject;
            }
        }

        // Extract text from memory files
        async function extractTextFromMemoryFiles(files, promptPrefix) {
            if (files.length === 0) {
                return "No files to process.";
            }
            
            // Update progress bar
            progressBar.style.width = '10%';
            
            try {
                // Process any PDF files first - convert them to images
                const pdfFiles = files.filter(file => file.isPdf);
                if (pdfFiles.length > 0) {
                    processingStatus.innerHTML = `<i class="fas fa-file-pdf"></i> Converting ${pdfFiles.length} PDF file(s) to images...`;
                    
                    // Process PDFs one at a time to avoid memory issues
                    for (let i = 0; i < pdfFiles.length; i++) {
                        processingStatus.innerHTML = `<i class="fas fa-file-pdf"></i> Converting PDF ${i+1}/${pdfFiles.length}: ${pdfFiles[i].name}...`;
                        await convertPdfToImages(pdfFiles[i]);
                        // Update progress
                        progressBar.style.width = `${10 + (i + 1) * 20 / pdfFiles.length}%`;
                    }
                }
                
                // Prepare images for processing
                const imagesToProcess = [];
                
                // Add regular image files
                const imageFiles = files.filter(file => !file.isPdf);
                for (const file of imageFiles) {
                    if (file.loaded && file.base64Data) {
                        imagesToProcess.push({
                            fileName: file.name,
                            base64Data: file.base64Data
                        });
                    }
                }
                
                // Add PDF page images
                let pdfPageCount = 0;
                for (const file of pdfFiles) {
                    if (file.loaded && file.pageImages && file.pageImages.length > 0) {
                        pdfPageCount += file.pageImages.length;
                        for (const page of file.pageImages) {
                            imagesToProcess.push({
                                fileName: `${file.name} - Page ${page.pageNum}`,
                                base64Data: page.base64Data
                            });
                        }
                    }
                }
                
                if (imagesToProcess.length === 0) {
                    return "No valid images could be processed.";
                }
                
                // Update status with image count information
                const pdfInfo = pdfPageCount > 0 ? ` (including ${pdfPageCount} PDF pages)` : '';
                processingStatus.innerHTML = `<i class="fas fa-brain"></i> Extracting text from ${imagesToProcess.length} images${pdfInfo}...`;
                progressBar.style.width = '40%';
                
                // Process images in batches to avoid memory issues
                const batchSize = 2; // Reduced batch size for better reliability
                const batches = Math.ceil(imagesToProcess.length / batchSize);
                let allResults = [];
                
                for (let batch = 0; batch < batches; batch++) {
                    const start = batch * batchSize;
                    const end = Math.min(start + batchSize, imagesToProcess.length);
                    const batchImages = imagesToProcess.slice(start, end);
                    
                    // Update status
                    processingStatus.innerHTML = `<i class="fas fa-brain"></i> Processing batch ${batch + 1}/${batches} (${batchImages.length} images)...`;
                    
                    // Process this batch
                    const batchResults = await processImageBatch(batchImages, promptPrefix);
                    allResults = allResults.concat(batchResults);
                    
                    // Update progress
                    progressBar.style.width = `${40 + (batch + 1) * 50 / batches}%`;
                    
                    // Add a small delay between batches to allow UI to update and prevent freezing
                    await new Promise(resolve => setTimeout(resolve, 300));
                }
                
                // Combine results with better formatting
                let combinedText = "";
                
                // First add regular image results
                const regularResults = allResults.filter(r => !r.fileName.includes('- Page '));
                if (regularResults.length > 0) {
                    combinedText += "=== IMAGE FILES ===\n\n";
                    for (const result of regularResults) {
                        combinedText += `--- ${result.fileName} ---\n\n${result.text}\n\n`;
                    }
                }
                
                // Then add PDF results, grouped by file
                const pdfResults = allResults.filter(r => r.fileName.includes('- Page '));
                if (pdfResults.length > 0) {
                    combinedText += "=== PDF FILES ===\n\n";
                    
                    // Group by PDF file name
                    const pdfGroups = {};
                    for (const result of pdfResults) {
                        const pdfName = result.fileName.split(' - Page ')[0];
                        if (!pdfGroups[pdfName]) {
                            pdfGroups[pdfName] = [];
                        }
                        pdfGroups[pdfName].push(result);
                    }
                    
                    // Add each PDF's content
                    for (const pdfName in pdfGroups) {
                        combinedText += `--- ${pdfName} ---\n\n`;
                        
                        // Sort pages by number
                        pdfGroups[pdfName].sort((a, b) => {
                            const pageA = parseInt(a.fileName.split('Page ')[1]);
                            const pageB = parseInt(b.fileName.split('Page ')[1]);
                            return pageA - pageB;
                        });
                        
                        // Add each page's content
                        for (const page of pdfGroups[pdfName]) {
                            combinedText += `[Page ${page.fileName.split('Page ')[1]}]\n${page.text}\n\n`;
                        }
                        
                        combinedText += "\n";
                    }
                }
                
                // Complete progress
                progressBar.style.width = '100%';
                processingStatus.innerHTML = '<i class="fas fa-check-circle"></i> Processing complete!';
                
                return combinedText || "No text could be extracted from the images.";
                
            } catch (error) {
                console.error("Error extracting text:", error);
                progressBar.style.width = '100%';
                processingStatus.innerHTML = '<i class="fas fa-exclamation-circle"></i> Error processing files';
                return `Error: ${error.message || 'Unknown error'}`;
            }
        }
        
        // Process a batch of images with Gemini 2.0 Flash
        // This function uses the Gemini 2.0 Flash model for better text extraction from images
        async function processImageBatch(images, promptPrefix) {
            const results = [];
            
            for (const image of images) {
                try {
                    // Check if this is a PDF-derived image
                    const isPdfImage = image.fileName.includes('- Page ');
                    
                    // Create the prompt for Gemini with specific instructions for PDF images
                    let prompt = promptPrefix || "Please analyze this image and extract all visible text.";
                    
                    // Add PDF-specific instructions if this is from a PDF
                    if (isPdfImage) {
                        prompt += " This image was converted from a PDF document. Please extract ALL text content visible in the image, maintaining the original formatting as much as possible. Include all paragraphs, bullet points, tables, and any other text elements. If there are multiple columns, process them from left to right, top to bottom.";
                    }
                    
                    console.log(`Processing image: ${image.fileName}`);
                    
                    // Call Gemini Vision API with retry logic
                    let response = null;
                    let retryCount = 0;
                    const maxRetries = 2;
                    
                    while (retryCount <= maxRetries) {
                        try {
                            response = await fetch(`https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent?key=${API_KEY}`, {
                                method: 'POST',
                                headers: {
                                    'Content-Type': 'application/json'
                                },
                                body: JSON.stringify({
                                    contents: [
                                        {
                                            parts: [
                                                { text: prompt },
                                                {
                                                    inline_data: {
                                                        mime_type: "image/jpeg",
                                                        data: image.base64Data
                                                    }
                                                }
                                            ]
                                        }
                                    ],
                                    generation_config: {
                                        temperature: 0,
                                        max_output_tokens: 8192,
                                        top_p: 0.95,
                                        top_k: 40
                                    }
                                })
                            });
                            
                            // If successful, break out of retry loop
                            if (response.ok) {
                                break;
                            } else {
                                const errorData = await response.json();
                                console.warn(`API error (attempt ${retryCount + 1}/${maxRetries + 1}): ${errorData.error?.message || response.statusText}`);
                                
                                // If we've reached max retries, throw the error
                                if (retryCount === maxRetries) {
                                    throw new Error(`API error: ${errorData.error?.message || response.statusText}`);
                                }
                                
                                // Wait before retrying (exponential backoff)
                                await new Promise(resolve => setTimeout(resolve, 1000 * Math.pow(2, retryCount)));
                                retryCount++;
                            }
                        } catch (fetchError) {
                            console.error(`Fetch error (attempt ${retryCount + 1}/${maxRetries + 1}):`, fetchError);
                            
                            // If we've reached max retries, throw the error
                            if (retryCount === maxRetries) {
                                throw fetchError;
                            }
                            
                            // Wait before retrying
                            await new Promise(resolve => setTimeout(resolve, 1000 * Math.pow(2, retryCount)));
                            retryCount++;
                        }
                    }
                    
                    const data = await response.json();
                    let text = data.candidates?.[0]?.content?.parts?.[0]?.text || "No text could be extracted from this image.";
                    
                    // Post-process the extracted text for PDF images
                    if (isPdfImage && text) {
                        // Remove any "I can see this is a PDF document..." explanatory text
                        text = text.replace(/^I can see this is a PDF document[^]*?(Here is the text content:|The text content is:|The extracted text is:)/i, '');
                        
                        // Remove any trailing explanations
                        text = text.replace(/(That's all the text I could extract from this PDF image\.|I've extracted all the text from this PDF image\.|This completes the text extraction from the PDF image\.)[^]*/i, '');
                        
                        // Trim extra whitespace
                        text = text.trim();
                    }
                    
                    console.log(`Successfully extracted text from: ${image.fileName}`);
                    
                    results.push({
                        fileName: image.fileName,
                        text: text
                    });
                    
                } catch (error) {
                    console.error(`Error processing image ${image.fileName}:`, error);
                    results.push({
                        fileName: image.fileName,
                        text: `Error extracting text: ${error.message || 'Unknown error'}`
                    });
                }
            }
            
            return results;
        }

        async function assessAndGrade() {
            // Show processing status and disable button
            processingContainer.classList.remove('hidden');
            processingStatus.innerHTML = '<i class="fas fa-spinner"></i>Assessing and grading...';
            progressBar.style.width = '50%';
            assessBtn.disabled = true;
            
            // Show assessment loading section with steps
            assessmentLoading.classList.remove('hidden');
            
            try {
                const testContent = testContentArea.value;
                const answerContent = answerContentArea.value;
                
                if (!testContent || !answerContent) {
                    throw new Error('Both test content and answer content must be available');
                }
                
                // Update step 1 - Analyzing test content
                updateAssessmentStep('step1', 'active', 'Processing...');
                await simulateDelay(1000); // Simulate processing time
                
                // Update step 2 - Comparing with answer key
                updateAssessmentStep('step1', 'completed', 'Completed');
                updateAssessmentStep('step2', 'active', 'Processing...');
                await simulateDelay(1500); // Simulate processing time
                
                // Use Gemini API to assess and grade
                const prompt = `
                I have a test paper with student answers and an answer key. 
                
                TEST PAPER WITH STUDENT ANSWERS:
                ${testContent}
                
                ANSWER KEY:
                ${answerContent}
                
                Please grade this test by comparing the student's answers to the answer key. 
                For each question:
                1. Determine if the answer is correct, partially correct, or incorrect
                2. Assign appropriate points (full points for correct, partial points for partially correct, zero for incorrect)
                
                Then provide:
                1. A total score and percentage
                2. A breakdown of points for each question
                3. Brief feedback on strengths and areas for improvement
                
                Format your response in a clear, structured way.`;
                
                // Update step 3 - Calculating scores
                updateAssessmentStep('step2', 'completed', 'Completed');
                updateAssessmentStep('step3', 'active', 'Processing...');
                await simulateDelay(1000); // Simulate processing time
                
                try {
                    const gradeResult = await generateTextContent(prompt);
                    
                    // Update step 4 - Generating feedback
                    updateAssessmentStep('step3', 'completed', 'Completed');
                    updateAssessmentStep('step4', 'active', 'Processing...');
                    await simulateDelay(1000); // Simulate processing time
                    
                    // Complete all steps
                    updateAssessmentStep('step4', 'completed', 'Completed');
                    
                    // Update progress bar to 100%
                    progressBar.style.width = '100%';
                    
                    // Short delay before showing results
                    await simulateDelay(500);
                    
                    // Display results
                    results.classList.remove('hidden');
                    gradingOutput.innerHTML = formatGradingResult(gradeResult);
                    
                    // Show the "Grade Another Student" button
                    document.getElementById('newStudentContainer').style.display = 'block';
                    
                    // Scroll to results section
                    results.scrollIntoView({ behavior: 'smooth' });
                } catch (apiError) {
                    console.error('API error during grading:', apiError);
                    throw new Error(`Error during grading: ${apiError.message || 'Failed to generate assessment'}`);
                }
            } catch (error) {
                console.error('Assessment error:', error);
                alert(`Error during assessment: ${error.message || 'An unknown error occurred'}`);
            } finally {
                // Hide processing status and enable button
                processingContainer.classList.add('hidden');
                assessmentLoading.classList.add('hidden');
                assessBtn.disabled = false;
                // Reset progress bar
                progressBar.style.width = '0%';
                // Reset assessment steps for next time
                resetAssessmentSteps();
            }
        }
        
        // Generate text content using Gemini 2.0 Flash
        // This function uses the Gemini 2.0 Flash model for text generation and analysis
        async function generateTextContent(prompt) {
            try {
                // Using the Gemini 2.0 Flash model for text generation
                const response = await fetch(`https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent?key=${API_KEY}`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        contents: [{
                            role: "user",
                            parts: [{ text: prompt }]
                        }],
                        generationConfig: {
                            temperature: 0,
                            topK: 40,
                            topP: 0.95,
                            maxOutputTokens: 8192,
                            responseMimeType: "text/plain"
                        }
                    })
                });

                if (!response.ok) {
                    const errorData = await response.json();
                    const errorMessage = errorData.error?.message || 'API request failed';
                    console.error('Text generation API error:', errorData);
                    throw new Error(`API Error: ${errorMessage}`);
                }

                const data = await response.json();
                
                // Check if the response has the expected structure
                if (!data || !data.candidates || !data.candidates[0] || !data.candidates[0].content || !data.candidates[0].content.parts) {
                    console.error('Unexpected API response structure:', data);
                    throw new Error('Received an invalid response from the API');
                }
                
                // Extract the text from the response
                const responseText = data.candidates[0].content.parts[0].text || '';
                return responseText;
            } catch (error) {
                console.error('Error in generateTextContent:', error);
                throw new Error(error.message || 'Failed to generate content');
            }
        }
        
        function formatGradingResult(result) {
            // Convert plain text to HTML with proper formatting
            let formattedResult = result
                .replace(/\n\n/g, '</p><p>')
                .replace(/\n/g, '<br>')
                .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
                .replace(/\*(.*?)\*/g, '<em>$1</em>');
                
            // Try to extract and highlight the score if present
            const scoreMatch = result.match(/Total Score:\s*(\d+(?:\.\d+)?)\s*\/\s*(\d+(?:\.\d+)?)/i) || 
                              result.match(/Score:\s*(\d+(?:\.\d+)?)\s*\/\s*(\d+(?:\.\d+)?)/i);
                              
            if (scoreMatch) {
                const score = scoreMatch[1];
                const total = scoreMatch[2];
                const percentage = Math.round((parseFloat(score) / parseFloat(total)) * 100);
                
                // Add a score summary at the top
                const scoreHTML = `
                <div class="score-container">
                    <div class="score-label">Total Score:</div>
                    <div class="score-value">${score}/${total} (${percentage}%)</div>
                </div>`;
                
                formattedResult = scoreHTML + '<p>' + formattedResult + '</p>';
            } else {
                formattedResult = '<p>' + formattedResult + '</p>';
            }
            
            return formattedResult;
        }

        // Helper function to update assessment step status
        function updateAssessmentStep(stepId, status, statusText) {
            const step = assessmentSteps[stepId];
            
            // Remove existing status classes
            step.classList.remove('active', 'completed');
            
            // Add new status class
            if (status) {
                step.classList.add(status);
            }
            
            // Update status text
            const statusElement = step.querySelector('.step-status');
            if (statusElement && statusText) {
                statusElement.textContent = statusText;
            }
        }
        
        // Reset all assessment steps to initial state
        function resetAssessmentSteps() {
            Object.keys(assessmentSteps).forEach(stepId => {
                const step = assessmentSteps[stepId];
                step.classList.remove('active', 'completed');
                const statusElement = step.querySelector('.step-status');
                if (statusElement) {
                    statusElement.textContent = 'Waiting...';
                }
            });
        }
        
        // Helper function to simulate delay for demonstration purposes
        function simulateDelay(ms) {
            return new Promise(resolve => setTimeout(resolve, ms));
        }

        function resetPage() {
            // Reset all file selections and clear all data
            fileStorage.testPapers = [];
            fileStorage.answerSheets = [];
            testPapersInput.value = '';
            answerSheetsInput.value = '';
            testPaperPreview.innerHTML = '';
            answerSheetPreview.innerHTML = '';
            textExtraction.classList.add('hidden');
            results.classList.add('hidden');
            processingContainer.classList.add('hidden');
            assessmentLoading.classList.add('hidden');
            progressBar.style.width = '0%';
            processingStatus.innerHTML = '<i class="fas fa-spinner"></i>Processing...';
            assessBtn.classList.add('hidden');
            
            // Hide the "Grade Another Student" button
            document.getElementById('newStudentContainer').style.display = 'none';
            
            // Reset assessment steps
            resetAssessmentSteps();
            
            // Reset text areas
            testContentArea.value = '';
            answerContentArea.value = '';
            gradingOutput.innerHTML = '';
            
            // Scroll to top of page
            window.scrollTo({ top: 0, behavior: 'smooth' });
        }
    </script>
</body>
</html>