<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PAIP - Markdown Viewer</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

        :root {
            --primary-green: #2e7d32;
            --light-green: #4caf50;
            --accent-green: #66bb6a;
            --secondary-blue: #1976d2;
            --accent-orange: #ff9800;
            --white: #ffffff;
            --light-gray: #f5f5f5;
            --medium-gray: #e0e0e0;
            --dark-gray: #333333;
            --text-gray: #666666;
            --shadow-light: 0 2px 8px rgba(0,0,0,0.1);
            --shadow-medium: 0 4px 12px rgba(0,0,0,0.15);
            --border-radius: 8px;
            --border-radius-small: 6px;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: var(--light-gray);
            min-height: 100vh;
            padding: 20px;
            margin: 0;
        }

        /* Navigation Styles */
        .navbar {
            background: var(--white);
            box-shadow: var(--shadow-light);
            border-bottom: 1px solid var(--medium-gray);
            position: sticky;
            top: 0;
            z-index: 1000;
            margin: -20px -20px 20px -20px;
        }

        .nav-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            height: 70px;
        }

        .nav-brand {
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--primary-green);
            text-decoration: none;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .nav-brand:hover {
            color: var(--light-green);
        }

        .nav-menu {
            display: flex;
            list-style: none;
            gap: 0;
            margin: 0;
            padding: 0;
        }

        .nav-item {
            position: relative;
        }

        .nav-link {
            display: block;
            padding: 15px 20px;
            color: var(--dark-gray);
            text-decoration: none;
            font-weight: 500;
            transition: all 0.3s ease;
            border-radius: var(--border-radius-small);
            margin: 0 5px;
        }

        .nav-link:hover {
            color: var(--primary-green);
            background: rgba(46, 125, 50, 0.1);
        }

        .nav-link.active {
            color: var(--primary-green);
            background: rgba(46, 125, 50, 0.15);
            font-weight: 600;
        }

        .hamburger {
            display: none;
            flex-direction: column;
            cursor: pointer;
            padding: 5px;
            border-radius: var(--border-radius-small);
            transition: background 0.3s ease;
        }

        .hamburger:hover {
            background: rgba(46, 125, 50, 0.1);
        }

        .hamburger span {
            width: 25px;
            height: 3px;
            background: var(--primary-green);
            margin: 3px 0;
            transition: 0.3s;
            border-radius: 2px;
        }

        .hamburger.active span:nth-child(1) {
            transform: rotate(-45deg) translate(-5px, 6px);
        }

        .hamburger.active span:nth-child(2) {
            opacity: 0;
        }

        .hamburger.active span:nth-child(3) {
            transform: rotate(45deg) translate(-5px, -6px);
        }

        /* Mobile Styles */
        @media (max-width: 768px) {
            .nav-menu {
                position: fixed;
                left: -100%;
                top: 70px;
                flex-direction: column;
                background: var(--white);
                width: 100%;
                text-align: center;
                transition: 0.3s;
                box-shadow: var(--shadow-medium);
                border-top: 1px solid var(--medium-gray);
                padding: 20px 0;
            }

            .nav-menu.active {
                left: 0;
            }

            .nav-item {
                margin: 5px 0;
            }

            .nav-link {
                padding: 15px 20px;
                margin: 0 20px;
                border-radius: var(--border-radius);
            }

            .hamburger {
                display: flex;
            }
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: var(--white);
            border-radius: var(--border-radius);
            padding: 40px;
            box-shadow: var(--shadow-light);
            border: 1px solid var(--medium-gray);
        }

        h1 {
            color: var(--primary-green);
            text-align: center;
            font-size: 2.5rem;
            font-weight: 600;
            margin-bottom: 10px;
        }

        .subtitle {
            text-align: center;
            color: var(--text-gray);
            font-size: 1.1rem;
            margin-bottom: 40px;
        }

        .section-card {
            background: var(--white);
            border-radius: var(--border-radius);
            padding: 25px;
            margin-bottom: 25px;
            box-shadow: var(--shadow-light);
            border: 1px solid var(--medium-gray);
            transition: box-shadow 0.2s ease;
        }

        .section-card:hover {
            box-shadow: var(--shadow-medium);
        }

        .section-title {
            font-size: 1.4rem;
            font-weight: 600;
            margin-bottom: 15px;
            color: var(--primary-green);
        }

        .upload-area {
            border: 2px dashed var(--light-green);
            text-align: center;
            transition: border-color 0.2s ease, background-color 0.2s ease;
            padding: 40px;
            border-radius: var(--border-radius);
        }

        .upload-area:hover {
            border-color: var(--primary-green);
            background-color: rgba(76, 175, 80, 0.05);
        }

        .upload-area.dragover {
            border-color: var(--primary-green);
            background-color: rgba(76, 175, 80, 0.1);
        }

        .btn {
            border: none;
            border-radius: var(--border-radius-small);
            cursor: pointer;
            font-family: inherit;
            font-weight: 500;
            transition: all 0.2s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
        }

        .btn:hover {
            transform: translateY(-1px);
        }

        .btn:active {
            transform: translateY(0);
        }

        .upload-btn {
            background: var(--light-green);
            color: white;
            padding: 12px 24px;
            font-size: 16px;
            margin: 10px;
            box-shadow: var(--shadow-light);
        }

        .upload-btn:hover {
            background: var(--primary-green);
            box-shadow: var(--shadow-medium);
        }

        .copy-btn {
            background: var(--accent-orange);
            color: white;
            padding: 12px 24px;
            font-size: 14px;
            margin: 10px;
            min-width: 180px;
            box-shadow: var(--shadow-light);
            transition: all 0.4s ease;
        }

        .copy-btn:hover {
            background: #f57c00;
            box-shadow: var(--shadow-medium);
        }

        .copy-btn.copied {
            background: var(--primary-green);
            transition: all 60s ease;
        }

        .copy-btn.copied:hover {
            background: var(--primary-green);
        }

        .copy-btn .checkmark {
            display: none;
        }

        .copy-btn.copied .checkmark {
            display: inline;
        }

        .copy-btn.copied .copy-text {
            display: none;
        }

        .copy-btn .copied-text {
            display: none;
        }

        .copy-btn.copied .copied-text {
            display: inline;
        }

        .reset-btn {
            background: var(--text-gray);
            color: white;
            padding: 10px 20px;
            font-size: 14px;
            margin: 10px;
            box-shadow: var(--shadow-light);
        }

        .reset-btn:hover {
            background: var(--dark-gray);
            box-shadow: var(--shadow-medium);
        }

        .file-info {
            background: rgba(76, 175, 80, 0.1);
            border: 1px solid rgba(76, 175, 80, 0.3);
            border-radius: var(--border-radius-small);
            padding: 15px;
            margin: 15px 0;
            font-size: 14px;
        }

        .controls {
            display: flex;
            gap: 10px;
            margin: 20px 0;
            flex-wrap: wrap;
        }

        .markdown-output {
            background: var(--white);
            border: 1px solid var(--medium-gray);
            border-radius: var(--border-radius);
            padding: 30px;
            margin-top: 20px;
            max-height: 70vh;
            overflow-y: auto;
            line-height: 1.6;
        }

        /* Markdown Content Styling */
        .markdown-output h1,
        .markdown-output h2,
        .markdown-output h3,
        .markdown-output h4,
        .markdown-output h5,
        .markdown-output h6 {
            color: var(--primary-green);
            margin-top: 1.5em;
            margin-bottom: 0.5em;
        }

        .markdown-output h1 {
            font-size: 2rem;
            border-bottom: 2px solid var(--light-green);
            padding-bottom: 0.3em;
        }

        .markdown-output h2 {
            font-size: 1.5rem;
            border-bottom: 1px solid var(--medium-gray);
            padding-bottom: 0.3em;
        }

        .markdown-output p {
            margin-bottom: 1em;
            color: var(--dark-gray);
        }

        .markdown-output code {
            background: var(--light-gray);
            padding: 2px 6px;
            border-radius: 3px;
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            font-size: 0.9em;
        }

        .markdown-output pre {
            background: var(--light-gray);
            border: 1px solid var(--medium-gray);
            border-radius: var(--border-radius-small);
            padding: 15px;
            overflow-x: auto;
            margin: 1em 0;
        }

        .markdown-output pre code {
            background: none;
            padding: 0;
        }

        .markdown-output blockquote {
            border-left: 4px solid var(--light-green);
            padding-left: 15px;
            margin: 1em 0;
            color: var(--text-gray);
            font-style: italic;
        }

        .markdown-output ul,
        .markdown-output ol {
            margin: 1em 0;
            padding-left: 2em;
        }

        .markdown-output li {
            margin-bottom: 0.5em;
        }

        .markdown-output a {
            color: var(--secondary-blue);
            text-decoration: none;
        }

        .markdown-output a:hover {
            text-decoration: underline;
        }

        .markdown-output table {
            border-collapse: collapse;
            width: 100%;
            margin: 1em 0;
        }

        .markdown-output th,
        .markdown-output td {
            border: 1px solid var(--medium-gray);
            padding: 8px 12px;
            text-align: left;
        }

        .markdown-output th {
            background: var(--light-gray);
            font-weight: 600;
        }

        .notification {
            padding: 15px 20px;
            border-radius: var(--border-radius-small);
            margin: 15px 0;
            border-left: 4px solid;
        }

        .notification.success {
            background: rgba(76, 175, 80, 0.1);
            color: var(--primary-green);
            border-left-color: var(--primary-green);
        }

        .notification.error {
            background: rgba(244, 67, 54, 0.1);
            color: #d32f2f;
            border-left-color: #f44336;
        }

        /* Footer Styling */
        .footer {
            background: linear-gradient(135deg, var(--primary-green) 0%, var(--light-green) 100%);
            color: white;
            text-align: center;
            padding: 30px 20px;
            margin-top: 50px;
            border-radius: var(--border-radius) var(--border-radius) 0 0;
            position: relative;
            overflow: hidden;
        }

        .footer::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 1px;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
        }

        .footer-content {
            max-width: 1200px;
            margin: 0 auto;
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 15px;
        }

        .footer-brand {
            font-size: 1.2rem;
            font-weight: 600;
            margin-bottom: 5px;
        }

        .footer-copyright {
            font-size: 0.85rem;
            opacity: 0.7;
            border-top: 1px solid rgba(255,255,255,0.2);
            padding-top: 15px;
            width: 100%;
        }

        .footer-powered {
            font-size: 0.8rem;
            opacity: 0.6;
            font-style: italic;
        }

        /* Paste Area Styles */
        .paste-area {
            width: 100%;
        }

        .markdown-textarea {
            width: 100%;
            min-height: 300px;
            padding: 20px;
            border: 2px solid var(--medium-gray);
            border-radius: var(--border-radius);
            font-family: 'Courier New', Monaco, monospace;
            font-size: 14px;
            line-height: 1.6;
            resize: vertical;
            background: var(--white);
            color: var(--dark-gray);
            transition: border-color 0.3s ease, box-shadow 0.3s ease;
            box-sizing: border-box;
        }

        .markdown-textarea:focus {
            outline: none;
            border-color: var(--primary-green);
            box-shadow: 0 0 0 3px rgba(46, 125, 50, 0.1);
        }

        .markdown-textarea::placeholder {
            color: var(--text-gray);
            opacity: 0.7;
        }

        .paste-controls {
            display: flex;
            gap: 15px;
            margin-top: 20px;
            flex-wrap: wrap;
        }

        .process-btn {
            background: var(--primary-green);
            color: white;
            padding: 12px 24px;
            font-size: 16px;
            font-weight: 600;
            box-shadow: var(--shadow-light);
        }

        .process-btn:hover {
            background: var(--light-green);
            box-shadow: var(--shadow-medium);
        }

        .clear-btn {
            background: var(--accent-orange);
            color: white;
            padding: 12px 24px;
            font-size: 16px;
            font-weight: 500;
        }

        .clear-btn:hover {
            background: #f57c00;
        }

        .sample-btn {
            background: var(--secondary-blue);
            color: white;
            padding: 12px 24px;
            font-size: 16px;
            font-weight: 500;
        }

        .sample-btn:hover {
            background: #1565c0;
        }

        /* Rendered Controls Styles */
        .rendered-controls {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }

        .rendered-controls .copy-btn {
            background: var(--secondary-blue);
            color: white;
            padding: 10px 20px;
            font-size: 14px;
            font-weight: 500;
            border-radius: var(--border-radius-small);
            transition: all 0.3s ease;
        }

        .rendered-controls .copy-btn:hover {
            background: #1565c0;
            transform: translateY(-1px);
        }

        .rendered-controls .copy-btn.copied {
            background: var(--light-green);
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .container {
                padding: 20px;
                margin: 10px;
            }

            h1 {
                font-size: 2rem;
            }

            .section-card {
                padding: 20px;
            }

            .controls {
                flex-direction: column;
            }

            .btn {
                width: 100%;
                justify-content: center;
            }

            .paste-controls {
                flex-direction: column;
            }

            .paste-controls .btn {
                width: 100%;
                margin-bottom: 10px;
            }

            .markdown-textarea {
                min-height: 250px;
                font-size: 16px; /* Prevent zoom on iOS */
            }

            .rendered-controls {
                width: 100%;
                justify-content: center;
            }

            .rendered-controls .copy-btn {
                flex: 1;
                min-width: 150px;
            }

            .footer {
                margin-top: 30px;
                padding: 25px 15px;
            }
        }
    </style>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/marked/9.1.2/marked.min.js"></script>
</head>
<body>
    <!-- Navigation Menu -->
    <nav class="navbar">
        <div class="nav-container">
            <a href="index.html" class="nav-brand">
                🎯 PAIP
            </a>
            <ul class="nav-menu" id="nav-menu">
                <li class="nav-item">
                    <a href="index.html" class="nav-link">🏠 Home</a>
                </li>
                <li class="nav-item">
                    <a href="gemini_file_upload_v3.html" class="nav-link">📄 Document Analysis</a>
                </li>
                <li class="nav-item">
                    <a href="gemini_format_post.html" class="nav-link">📊 PDF to CSV</a>
                </li>
                <li class="nav-item">
                    <a href="paip_qc.html" class="nav-link">🔍 Quality Check</a>
                </li>
                <li class="nav-item">
                    <a href="html_markdownviewer.html" class="nav-link active">📝 Markdown Viewer</a>
                </li>
            </ul>
            <div class="hamburger" id="hamburger">
                <span></span>
                <span></span>
                <span></span>
            </div>
        </div>
    </nav>

    <div class="container">
        <h1>PAIP - Markdown Viewer</h1>
        <p class="subtitle">
            📝 Upload markdown files or paste text directly for beautiful rendering
        </p>

        <!-- Upload Section -->
        <div class="section-card">
            <h3 class="section-title">📤 Upload Markdown File</h3>
            <div class="upload-area" id="uploadArea">
                <div style="font-size: 48px; margin-bottom: 20px; opacity: 0.6;">📄</div>
                <p style="color: var(--text-gray); margin-bottom: 20px;">
                    Drag and drop a markdown file here or click to select
                </p>
                <input type="file" id="fileInput" accept=".md,.markdown,.txt" style="display: none;" />
                <button class="btn upload-btn" onclick="document.getElementById('fileInput').click()">
                    <span>📁</span>
                    Choose Markdown File
                </button>
            </div>

            <!-- File Info -->
            <div class="file-info" id="fileInfo" style="display: none;">
                <div id="fileDetails"></div>
            </div>

            <!-- Controls -->
            <div class="controls" id="controls" style="display: none;">
                <button class="btn copy-btn" id="copyBtn" onclick="copyMarkdownContent()">
                    <span class="checkmark">✅</span>
                    <span class="copy-text">📋 Copy Markdown</span>
                    <span class="copied-text">✅ Copied!</span>
                </button>
                <button class="btn copy-btn" id="copyHtmlBtn" onclick="copyHtmlContent()">
                    <span class="checkmark">✅</span>
                    <span class="copy-text">🌐 Copy HTML</span>
                    <span class="copied-text">✅ HTML Copied!</span>
                </button>
                <button class="btn reset-btn" onclick="clearViewer()">
                    <span>🗑️</span>
                    Clear
                </button>
            </div>
        </div>

        <!-- Paste Section -->
        <div class="section-card">
            <h3 class="section-title">✏️ Paste Markdown Text</h3>
            <p style="color: var(--text-gray); margin-bottom: 15px;">
                Paste your markdown content directly into the text area below
            </p>
            <p style="color: var(--text-gray); font-size: 0.9em; margin-bottom: 20px; font-style: italic;">
                💡 <strong>Keyboard shortcuts:</strong> Ctrl+Enter to render, Ctrl+Shift+C to clear, Ctrl+Shift+S for sample, Ctrl+Shift+T to copy formatted text
            </p>

            <div class="paste-area">
                <textarea
                    id="markdownTextarea"
                    class="markdown-textarea"
                    placeholder="Paste your markdown content here...

# Example Markdown

## Heading 2

This is a **bold** text and this is *italic* text.

- List item 1
- List item 2
- List item 3

```javascript
console.log('Hello, World!');
```

[Link example](https://example.com)"
                    rows="15"></textarea>

                <div class="paste-controls">
                    <button class="btn process-btn" id="processTextBtn" onclick="processMarkdownText()">
                        <span>🚀</span>
                        Render Markdown
                    </button>
                    <button class="btn clear-btn" onclick="clearTextarea()">
                        <span>🗑️</span>
                        Clear Text
                    </button>
                    <button class="btn sample-btn" onclick="loadSampleMarkdown()">
                        <span>📝</span>
                        Load Sample
                    </button>
                </div>
            </div>
        </div>

        <!-- Markdown Output -->
        <div class="section-card" id="outputSection" style="display: none;">
            <div style="display: flex; justify-content: space-between; align-items: flex-start; margin-bottom: 20px; flex-wrap: wrap; gap: 15px;">
                <div>
                    <h3 class="section-title" style="margin-bottom: 5px;">📖 Rendered Markdown</h3>
                    <p style="color: var(--text-gray); font-size: 0.9em; margin: 0;">
                        Beautiful HTML rendering with copy-paste formatting support
                    </p>
                </div>
                <div class="rendered-controls">
                    <button class="btn copy-btn" id="copyRenderedTextBtn" onclick="copyRenderedText()" title="Copy the formatted text content - preserves bold, italic, lists, etc. when pasted into rich text editors">
                        <span class="checkmark">✅</span>
                        <span class="copy-text">📝 Copy Formatted</span>
                        <span class="copied-text">✅ Formatted Copied!</span>
                    </button>
                </div>
            </div>
            <div class="markdown-output" id="markdownOutput"></div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="footer">
        <div class="footer-content">
            <div class="footer-brand">PAIP - Markdown Viewer</div>
            <div class="footer-copyright">
                <div>© 2025 Dakoii Systems. All rights reserved.</div>
                <div class="footer-powered">Powered by Dakoii AI</div>
            </div>
        </div>
    </footer>

    <!-- Notification Container -->
    <div id="notificationContainer"></div>

    <script>
        // Global variables
        let currentMarkdownContent = '';
        let currentHtmlContent = '';

        // ===== NAVIGATION FUNCTIONALITY =====

        function initializeNavigation() {
            const hamburger = document.getElementById('hamburger');
            const navMenu = document.getElementById('nav-menu');

            if (hamburger && navMenu) {
                hamburger.addEventListener('click', function() {
                    hamburger.classList.toggle('active');
                    navMenu.classList.toggle('active');
                });

                // Close menu when clicking on a link
                const navLinks = document.querySelectorAll('.nav-link');
                navLinks.forEach(link => {
                    link.addEventListener('click', function() {
                        hamburger.classList.remove('active');
                        navMenu.classList.remove('active');
                    });
                });

                // Close menu when clicking outside
                document.addEventListener('click', function(event) {
                    const isClickInsideNav = navMenu.contains(event.target) || hamburger.contains(event.target);
                    if (!isClickInsideNav && navMenu.classList.contains('active')) {
                        hamburger.classList.remove('active');
                        navMenu.classList.remove('active');
                    }
                });

                // Handle window resize
                window.addEventListener('resize', function() {
                    if (window.innerWidth > 768) {
                        hamburger.classList.remove('active');
                        navMenu.classList.remove('active');
                    }
                });
            }
        }

        // ===== KEYBOARD SHORTCUTS =====

        function initializeKeyboardShortcuts() {
            document.addEventListener('keydown', function(event) {
                // Ctrl/Cmd + Enter: Render markdown from textarea
                if ((event.ctrlKey || event.metaKey) && event.key === 'Enter') {
                    const textarea = document.getElementById('markdownTextarea');
                    if (document.activeElement === textarea && textarea.value.trim()) {
                        event.preventDefault();
                        processMarkdownText();
                    }
                }

                // Ctrl/Cmd + Shift + C: Clear textarea
                if ((event.ctrlKey || event.metaKey) && event.shiftKey && event.key === 'C') {
                    event.preventDefault();
                    clearTextarea();
                }

                // Ctrl/Cmd + Shift + S: Load sample
                if ((event.ctrlKey || event.metaKey) && event.shiftKey && event.key === 'S') {
                    event.preventDefault();
                    loadSampleMarkdown();
                }

                // Ctrl/Cmd + Shift + T: Copy rendered text
                if ((event.ctrlKey || event.metaKey) && event.shiftKey && event.key === 'T') {
                    const outputSection = document.getElementById('outputSection');
                    if (outputSection.style.display !== 'none') {
                        event.preventDefault();
                        copyRenderedText();
                    }
                }
            });
        }

        // Initialize the application
        document.addEventListener('DOMContentLoaded', function() {
            initializeNavigation();
            initializeDragDrop();
            initializeFileInput();
            initializeKeyboardShortcuts();
            console.log('PAIP Markdown Viewer - Ready!');
        });

        // Initialize drag and drop functionality
        function initializeDragDrop() {
            const uploadArea = document.getElementById('uploadArea');

            uploadArea.addEventListener('dragover', handleDragOver);
            uploadArea.addEventListener('dragleave', handleDragLeave);
            uploadArea.addEventListener('drop', handleDrop);
        }

        function handleDragOver(e) {
            e.preventDefault();
            document.getElementById('uploadArea').classList.add('dragover');
        }

        function handleDragLeave(e) {
            e.preventDefault();
            document.getElementById('uploadArea').classList.remove('dragover');
        }

        function handleDrop(e) {
            e.preventDefault();
            document.getElementById('uploadArea').classList.remove('dragover');

            const files = Array.from(e.dataTransfer.files).filter(file =>
                file.name.toLowerCase().endsWith('.md') ||
                file.name.toLowerCase().endsWith('.markdown') ||
                file.name.toLowerCase().endsWith('.txt')
            );

            if (files.length > 0) {
                handleFile(files[0]);
            } else {
                showNotification('Please select a valid markdown file (.md, .markdown, .txt)', 'error');
            }
        }

        // Initialize file input
        function initializeFileInput() {
            document.getElementById('fileInput').addEventListener('change', function(e) {
                if (e.target.files.length > 0) {
                    handleFile(e.target.files[0]);
                }
            });
        }

        // Handle file processing
        async function handleFile(file) {
            try {
                // Validate file type
                const validExtensions = ['.md', '.markdown', '.txt'];
                const fileExtension = '.' + file.name.split('.').pop().toLowerCase();

                if (!validExtensions.includes(fileExtension)) {
                    showNotification('Please select a valid markdown file (.md, .markdown, .txt)', 'error');
                    return;
                }

                // Read file content
                const content = await readFileContent(file);
                currentMarkdownContent = content;

                // Display file info
                displayFileInfo(file);

                // Render markdown
                renderMarkdown(content);

                // Show controls and output
                document.getElementById('controls').style.display = 'flex';
                document.getElementById('outputSection').style.display = 'block';

                showNotification('Markdown file loaded and rendered successfully!', 'success');

            } catch (error) {
                console.error('Error processing file:', error);
                showNotification('Error reading file: ' + error.message, 'error');
            }
        }

        // Read file content
        function readFileContent(file) {
            return new Promise((resolve, reject) => {
                const reader = new FileReader();
                reader.onload = function(e) {
                    resolve(e.target.result);
                };
                reader.onerror = function(e) {
                    reject(new Error('Failed to read file'));
                };
                reader.readAsText(file);
            });
        }

        // Display file information
        function displayFileInfo(file) {
            const fileInfo = document.getElementById('fileInfo');
            const fileDetails = document.getElementById('fileDetails');

            const fileSize = formatFileSize(file.size);
            const lastModified = new Date(file.lastModified).toLocaleString();

            fileDetails.innerHTML = `
                <strong>📄 File:</strong> ${file.name}<br>
                <strong>📏 Size:</strong> ${fileSize}<br>
                <strong>📅 Modified:</strong> ${lastModified}<br>
                <strong>📝 Type:</strong> ${file.type || 'text/markdown'}
            `;

            fileInfo.style.display = 'block';
        }

        // Render markdown content
        function renderMarkdown(content) {
            try {
                // Configure marked options
                marked.setOptions({
                    breaks: true,
                    gfm: true,
                    headerIds: true,
                    mangle: false
                });

                // Convert markdown to HTML
                const html = marked.parse(content);
                currentHtmlContent = html;

                // Display rendered content
                document.getElementById('markdownOutput').innerHTML = html;

            } catch (error) {
                console.error('Error rendering markdown:', error);
                showNotification('Error rendering markdown: ' + error.message, 'error');
            }
        }

        // Copy markdown content to clipboard
        async function copyMarkdownContent() {
            if (!currentMarkdownContent) {
                showNotification('No markdown content to copy. Please upload a file first.', 'error');
                return;
            }

            try {
                await navigator.clipboard.writeText(currentMarkdownContent);
                showCopySuccess('copyBtn');
                showNotification('Markdown content copied to clipboard!', 'success');
            } catch (error) {
                // Fallback for older browsers
                const textArea = document.createElement('textarea');
                textArea.value = currentMarkdownContent;
                document.body.appendChild(textArea);
                textArea.select();
                document.execCommand('copy');
                document.body.removeChild(textArea);
                showCopySuccess('copyBtn');
                showNotification('Markdown content copied to clipboard!', 'success');
            }
        }

        // Copy HTML content to clipboard
        async function copyHtmlContent() {
            if (!currentHtmlContent) {
                showNotification('No HTML content to copy. Please upload a file first.', 'error');
                return;
            }

            try {
                await navigator.clipboard.writeText(currentHtmlContent);
                showCopySuccess('copyHtmlBtn');
                showNotification('HTML content copied to clipboard!', 'success');
            } catch (error) {
                // Fallback for older browsers
                const textArea = document.createElement('textarea');
                textArea.value = currentHtmlContent;
                document.body.appendChild(textArea);
                textArea.select();
                document.execCommand('copy');
                document.body.removeChild(textArea);
                showCopySuccess('copyHtmlBtn');
                showNotification('HTML content copied to clipboard!', 'success');
            }
        }

        // Copy rendered text content with formatting to clipboard
        async function copyRenderedText() {
            const markdownOutput = document.getElementById('markdownOutput');

            if (!markdownOutput || !markdownOutput.innerHTML.trim()) {
                showNotification('No rendered content to copy. Please render some markdown first.', 'error');
                return;
            }

            try {
                // Create a ClipboardItem with both HTML and plain text
                const htmlContent = markdownOutput.innerHTML;
                const textContent = markdownOutput.textContent || markdownOutput.innerText;

                // Check if the browser supports writing HTML to clipboard
                if (navigator.clipboard && navigator.clipboard.write) {
                    const clipboardItem = new ClipboardItem({
                        'text/html': new Blob([htmlContent], { type: 'text/html' }),
                        'text/plain': new Blob([textContent], { type: 'text/plain' })
                    });

                    await navigator.clipboard.write([clipboardItem]);
                    showCopySuccess('copyRenderedTextBtn');
                    showNotification('Formatted text copied to clipboard! Paste into rich text editors to preserve formatting.', 'success');
                } else {
                    // Fallback: try to copy HTML using the older API
                    await copyFormattedTextFallback(htmlContent, textContent);
                }
            } catch (error) {
                console.log('Primary copy method failed, trying fallback:', error);
                // Fallback for browsers that don't support ClipboardItem
                try {
                    const htmlContent = markdownOutput.innerHTML;
                    const textContent = markdownOutput.textContent || markdownOutput.innerText;
                    await copyFormattedTextFallback(htmlContent, textContent);
                } catch (fallbackError) {
                    console.error('All copy methods failed:', fallbackError);
                    showNotification('Copy failed. Please try selecting and copying the content manually.', 'error');
                }
            }
        }

        // Fallback method for copying formatted text
        async function copyFormattedTextFallback(htmlContent, textContent) {
            // Create a temporary div with the HTML content
            const tempDiv = document.createElement('div');
            tempDiv.innerHTML = htmlContent;
            tempDiv.style.position = 'absolute';
            tempDiv.style.left = '-9999px';
            tempDiv.style.top = '-9999px';
            tempDiv.style.opacity = '0';
            tempDiv.style.pointerEvents = 'none';

            document.body.appendChild(tempDiv);

            try {
                // Select the content
                const range = document.createRange();
                range.selectNodeContents(tempDiv);
                const selection = window.getSelection();
                selection.removeAllRanges();
                selection.addRange(range);

                // Try to copy with formatting
                const success = document.execCommand('copy');

                if (success) {
                    showCopySuccess('copyRenderedTextBtn');
                    showNotification('Formatted text copied to clipboard! Paste into rich text editors to preserve formatting.', 'success');
                } else {
                    throw new Error('execCommand copy failed');
                }
            } finally {
                // Clean up
                document.body.removeChild(tempDiv);
                window.getSelection().removeAllRanges();
            }
        }

        // Show copy success visual feedback
        function showCopySuccess(buttonId) {
            const copyBtn = document.getElementById(buttonId);

            // Clear any existing timeout
            if (copyBtn.fadeTimeout) {
                clearTimeout(copyBtn.fadeTimeout);
            }

            // Add copied class immediately (green background + checkmark)
            copyBtn.classList.add('copied');

            // Set timeout to fade back to orange after 1 minute (60 seconds)
            copyBtn.fadeTimeout = setTimeout(() => {
                copyBtn.classList.remove('copied');
                copyBtn.fadeTimeout = null;
            }, 60000); // 60 seconds = 1 minute
        }

        // ===== PASTE FUNCTIONALITY =====

        // Process markdown text from textarea
        function processMarkdownText() {
            const textarea = document.getElementById('markdownTextarea');
            const markdownText = textarea.value.trim();

            if (!markdownText) {
                showNotification('Please enter some markdown content to render.', 'error');
                return;
            }

            try {
                // Store the markdown content
                currentMarkdownContent = markdownText;

                // Convert markdown to HTML
                currentHtmlContent = marked.parse(markdownText);

                // Display the rendered markdown
                const outputSection = document.getElementById('outputSection');
                const markdownOutput = document.getElementById('markdownOutput');

                markdownOutput.innerHTML = currentHtmlContent;
                outputSection.style.display = 'block';

                // Show controls
                const controls = document.getElementById('controls');
                controls.style.display = 'flex';

                // Scroll to output
                outputSection.scrollIntoView({ behavior: 'smooth', block: 'start' });

                showNotification('Markdown rendered successfully!', 'success');

            } catch (error) {
                console.error('Error processing markdown:', error);
                showNotification('Error processing markdown: ' + error.message, 'error');
            }
        }

        // Clear the textarea
        function clearTextarea() {
            const textarea = document.getElementById('markdownTextarea');
            textarea.value = '';
            textarea.focus();
            showNotification('Text area cleared.', 'success');
        }

        // Load sample markdown content
        function loadSampleMarkdown() {
            const sampleMarkdown = `# Welcome to PAIP Markdown Viewer

## Features

This markdown viewer supports:

- **Bold text** and *italic text*
- Lists (ordered and unordered)
- Code blocks and \`inline code\`
- Links and images
- Tables
- Blockquotes
- And much more!

## Code Example

\`\`\`javascript
function greetUser(name) {
    console.log(\`Hello, \${name}! Welcome to PAIP.\`);
}

greetUser('Developer');
\`\`\`

## Table Example

| Feature | Status | Priority |
|---------|--------|----------|
| File Upload | ✅ Complete | High |
| Text Paste | ✅ Complete | High |
| Copy Functions | ✅ Complete | Medium |
| Mobile Support | ✅ Complete | High |

## Blockquote Example

> "The best way to predict the future is to create it."
>
> — Peter Drucker

## Links

- [PAIP Home](index.html)
- [Document Analysis](gemini_file_upload_v3.html)
- [PDF to CSV](gemini_format_post.html)
- [Quality Check](paip_qc.html)

---

**Happy markdown editing!** 🎉`;

            const textarea = document.getElementById('markdownTextarea');
            textarea.value = sampleMarkdown;
            showNotification('Sample markdown loaded! Click "Render Markdown" to see the result.', 'success');
        }

        // Clear the viewer
        function clearViewer() {
            // Reset content
            currentMarkdownContent = '';
            currentHtmlContent = '';

            // Hide sections
            document.getElementById('fileInfo').style.display = 'none';
            document.getElementById('controls').style.display = 'none';
            document.getElementById('outputSection').style.display = 'none';

            // Clear content
            document.getElementById('markdownOutput').innerHTML = '';
            document.getElementById('fileInput').value = '';
            document.getElementById('markdownTextarea').value = '';

            // Reset copy button states
            const copyBtns = ['copyBtn', 'copyHtmlBtn', 'copyRenderedTextBtn'];
            copyBtns.forEach(btnId => {
                const btn = document.getElementById(btnId);
                btn.classList.remove('copied');
                if (btn.fadeTimeout) {
                    clearTimeout(btn.fadeTimeout);
                    btn.fadeTimeout = null;
                }
            });

            showNotification('Viewer cleared successfully!', 'success');
        }

        // Utility functions
        function formatFileSize(bytes) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }

        function showNotification(message, type) {
            // Remove existing notifications
            const existingNotifications = document.querySelectorAll('.notification');
            existingNotifications.forEach(notification => notification.remove());

            const notification = document.createElement('div');
            notification.className = `notification ${type}`;
            notification.textContent = message;

            // Insert at the top of the container
            const container = document.querySelector('.container');
            container.insertBefore(notification, container.firstChild);

            // Auto-remove after 5 seconds
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.remove();
                }
            }, 5000);
        }
    </script>
</body>
</html>