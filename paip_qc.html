<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PAIP - AI Quality Check System</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1400px;
            margin: 0 auto;
            padding: 0;
            background-color: #f5f5f5;
        }

        /* Simple Navigation */
        .simple-nav {
            background: white;
            padding: 15px 20px;
            margin-bottom: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 15px;
        }

        .nav-brand-simple {
            font-size: 1.5em;
            font-weight: bold;
            color: #2e7d32;
            text-decoration: none;
        }

        .nav-links-simple {
            display: flex;
            gap: 20px;
            flex-wrap: wrap;
        }

        .nav-link-simple {
            color: #666;
            text-decoration: none;
            padding: 8px 16px;
            border-radius: 5px;
            transition: all 0.3s;
            font-weight: 500;
        }

        .nav-link-simple:hover {
            background: #e8f5e8;
            color: #2e7d32;
        }

        .nav-link-simple.active {
            background: #2e7d32;
            color: white;
        }

        .main-content {
            padding: 20px;
        }

        @media (max-width: 768px) {
            .simple-nav {
                flex-direction: column;
                text-align: center;
            }

            .nav-links-simple {
                justify-content: center;
            }
        }

        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
            font-size: 2.5em;
        }

        .app-subtitle {
            text-align: center;
            color: #666;
            margin-bottom: 40px;
            font-size: 1.2em;
        }

        .section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            background-color: #fafafa;
        }

        .section h3 {
            color: #2e7d32;
            margin-top: 0;
            margin-bottom: 15px;
            font-size: 1.3em;
        }

        .upload-section {
            border: 2px dashed #ddd;
            border-radius: 10px;
            padding: 40px;
            text-align: center;
            margin-bottom: 20px;
            transition: border-color 0.3s;
            background-color: white;
        }

        .upload-section:hover {
            border-color: #2e7d32;
        }

        .upload-section.dragover {
            border-color: #2e7d32;
            background-color: #e8f5e8;
        }

        #fileInput {
            display: none;
        }

        .upload-btn {
            background-color: #2e7d32;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px;
            transition: background-color 0.3s;
        }

        .upload-btn:hover {
            background-color: #1b5e20;
        }

        .submit-btn {
            background-color: #4caf50;
            color: white;
            padding: 15px 30px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 18px;
            margin: 20px auto;
            display: block;
            transition: background-color 0.3s;
        }

        .submit-btn:hover:not(:disabled) {
            background-color: #45a049;
        }

        .submit-btn:disabled {
            background-color: #cccccc;
            cursor: not-allowed;
        }

        .criteria-section {
            margin-bottom: 30px;
        }

        .criteria-textarea {
            width: 100%;
            min-height: 150px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-family: Arial, sans-serif;
            font-size: 14px;
            resize: vertical;
            box-sizing: border-box;
        }

        .file-info {
            background-color: #e8f5e8;
            padding: 15px;
            border-radius: 5px;
            margin-top: 15px;
            text-align: left;
            display: none;
        }

        .file-list {
            margin-top: 15px;
        }

        .file-item {
            background-color: white;
            padding: 10px;
            margin: 5px 0;
            border-radius: 5px;
            border: 1px solid #ddd;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .file-item .file-name {
            font-weight: bold;
            color: #333;
        }

        .file-item .file-size {
            color: #666;
            font-size: 0.9em;
        }

        .remove-file {
            background-color: #f44336;
            color: white;
            border: none;
            padding: 5px 10px;
            border-radius: 3px;
            cursor: pointer;
            font-size: 12px;
        }

        .remove-file:hover {
            background-color: #d32f2f;
        }

        .progress-section {
            display: none;
            margin-top: 30px;
            padding: 20px;
            background-color: #f0f8ff;
            border-radius: 8px;
            border: 1px solid #2196f3;
        }

        .progress-bar {
            width: 100%;
            height: 20px;
            background-color: #e0e0e0;
            border-radius: 10px;
            overflow: hidden;
            margin: 10px 0;
        }

        .progress-fill {
            height: 100%;
            background-color: #4caf50;
            width: 0%;
            transition: width 0.3s;
        }

        .results-section {
            display: none;
            margin-top: 30px;
            padding: 20px;
            background-color: #f9f9f9;
            border-radius: 8px;
            border: 1px solid #ddd;
        }

        .quality-check-results {
            background-color: white;
            padding: 20px;
            border-radius: 8px;
            margin-top: 15px;
        }

        .flaw-item {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 5px;
            padding: 15px;
            margin: 10px 0;
            border-left: 4px solid #ff9800;
        }

        .flaw-item.critical {
            background-color: #f8d7da;
            border-color: #f5c6cb;
            border-left-color: #dc3545;
        }

        .flaw-item.warning {
            background-color: #fff3cd;
            border-color: #ffeaa7;
            border-left-color: #ffc107;
        }

        .flaw-item.info {
            background-color: #d1ecf1;
            border-color: #bee5eb;
            border-left-color: #17a2b8;
        }

        .flaw-title {
            font-weight: bold;
            margin-bottom: 8px;
            color: #333;
        }

        .flaw-description {
            color: #666;
            margin-bottom: 8px;
        }

        .flaw-file {
            font-size: 0.9em;
            color: #888;
            font-style: italic;
        }

        .spinner {
            border: 3px solid #f3f3f3;
            border-top: 3px solid #4caf50;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            animation: spin 1s linear infinite;
            display: inline-block;
            margin-left: 10px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .error {
            background-color: #f8d7da;
            color: #721c24;
            padding: 15px;
            border-radius: 5px;
            margin: 15px 0;
            border: 1px solid #f5c6cb;
        }

        .success {
            background-color: #d4edda;
            color: #155724;
            padding: 15px;
            border-radius: 5px;
            margin: 15px 0;
            border: 1px solid #c3e6cb;
        }

        .copy-btn {
            background-color: #17a2b8;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            margin-top: 10px;
            transition: all 0.3s;
        }

        .copy-btn:hover {
            background-color: #138496;
        }

        .copy-btn.copied {
            background-color: #28a745;
        }

        .copy-btn.copied::after {
            content: " ✓";
        }

        .footer {
            text-align: center;
            margin-top: 50px;
            padding: 20px;
            border-top: 1px solid #e0e0e0;
            color: #666;
            font-size: 0.9em;
        }

        .footer a {
            color: #2e7d32;
            text-decoration: none;
        }

        .footer a:hover {
            text-decoration: underline;
        }
    </style>
</head>
<body>
    <!-- Simple Navigation -->
    <nav class="simple-nav">
        <a href="index.html" class="nav-brand-simple">🎯 PAIP</a>
        <div class="nav-links-simple">
            <a href="index.html" class="nav-link-simple">🏠 Home</a>
            <a href="gemini_file_upload_v3.html" class="nav-link-simple">📄 Document Analysis</a>
            <a href="gemini_format_post.html" class="nav-link-simple">📊 PDF to CSV</a>
            <a href="paip_qc.html" class="nav-link-simple active">🔍 Quality Check</a>
            <a href="html_markdownviewer.html" class="nav-link-simple">📝 Markdown Viewer</a>
        </div>
    </nav>

    <div class="main-content">
        <div class="container">
        <h1>PAIP - AI Quality Check System</h1>
        <p class="app-subtitle">Positions AI Processing - Quality Assurance for Job Description Files</p>

        <div class="section">
            <h3>📁 Upload Job Description Files</h3>
            <p>Upload multiple PDF files containing job descriptions for quality verification. The AI will analyze each file for accuracy, consistency, and compliance with standards.</p>

            <div class="upload-section" id="uploadSection">
                <h4>Select Multiple PDF Files</h4>
                <p>Drag and drop PDF files here or click to select multiple files</p>
                <input type="file" id="fileInput" accept=".pdf" multiple />
                <button class="upload-btn" onclick="document.getElementById('fileInput').click()">
                    Choose PDF Files
                </button>

                <div class="file-info" id="fileInfo">
                    <strong>Selected Files:</strong>
                    <div class="file-list" id="fileList"></div>
                </div>
            </div>
        </div>

        <div class="section criteria-section">
            <h3>📋 Quality Check Criteria</h3>
            <p>Define the criteria that the AI should use to evaluate the job description files. You can customize these criteria based on your organization's standards.</p>

            <textarea class="criteria-textarea" id="criteriaInput" placeholder="Enter quality check criteria (one per line)...">File name matches position title and reference code 
                Position name corresponds correctly to the position reference code
</textarea>
        </div>

        <button class="submit-btn" id="submitBtn" onclick="startQualityCheck()" disabled>
            <span id="buttonText">Select PDF files first</span>
            <span id="loadingSpinner" class="spinner" style="display: none;"></span>
        </button>

        <div class="progress-section" id="progressSection">
            <h3>🔍 Quality Check in Progress</h3>
            <div class="progress-bar">
                <div class="progress-fill" id="progressFill"></div>
            </div>
            <div id="progressText">Initializing quality check...</div>
            <div id="currentFileStatus"></div>
        </div>

        <div class="results-section" id="resultsSection">
            <h3>📊 Quality Check Results</h3>
            <div id="summaryStats"></div>
            <div class="quality-check-results" id="qualityResults"></div>
            <button class="copy-btn" onclick="copyResults()" id="copyBtn">
                Copy Results to Clipboard
            </button>
        </div>
    </div>

    <div class="footer">
        <p>PAIP - Positions AI Processing | Developed by <a href="#">Dakoii Systems</a> | Copyright © 2025 | Powered by Dakoii AI</p>
    </div>

    <script>
        // Gemini AI Configuration
        const GEMINI_API_KEY = 'AIzaSyApNBtEwylsW_q5zUOvIDP3pj87WDShSLA'; // Replace with your actual API key
        const GEMINI_API_URL = 'https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash-lite-preview-06-17:generateContent';

        // Global variables
        let selectedFiles = [];
        let qualityCheckResults = [];
        let isProcessing = false;

        // Initialize drag and drop functionality
        document.addEventListener('DOMContentLoaded', function() {
            const uploadSection = document.getElementById('uploadSection');
            const fileInput = document.getElementById('fileInput');

            // Drag and drop events
            uploadSection.addEventListener('dragover', handleDragOver);
            uploadSection.addEventListener('dragleave', handleDragLeave);
            uploadSection.addEventListener('drop', handleDrop);

            // File input change event
            fileInput.addEventListener('change', handleFileSelect);

            console.log('PAIP Quality Check System - Ready');
        });

        function handleDragOver(e) {
            e.preventDefault();
            e.stopPropagation();
            document.getElementById('uploadSection').classList.add('dragover');
        }

        function handleDragLeave(e) {
            e.preventDefault();
            e.stopPropagation();
            document.getElementById('uploadSection').classList.remove('dragover');
        }

        function handleDrop(e) {
            e.preventDefault();
            e.stopPropagation();
            document.getElementById('uploadSection').classList.remove('dragover');

            const files = Array.from(e.dataTransfer.files).filter(file => file.type === 'application/pdf');
            if (files.length > 0) {
                addFiles(files);
            } else {
                showError('Please select valid PDF files only.');
            }
        }

        function handleFileSelect(e) {
            const files = Array.from(e.target.files).filter(file => file.type === 'application/pdf');
            if (files.length > 0) {
                addFiles(files);
            } else {
                showError('Please select valid PDF files only.');
            }
        }

        function addFiles(files) {
            // Add new files to the selected files array
            files.forEach(file => {
                // Check if file is already selected
                const exists = selectedFiles.some(existingFile =>
                    existingFile.name === file.name && existingFile.size === file.size
                );

                if (!exists) {
                    selectedFiles.push(file);
                }
            });

            updateFileList();
            updateSubmitButton();
            showSuccess(`${files.length} file(s) added successfully!`);
        }

        function removeFile(index) {
            selectedFiles.splice(index, 1);
            updateFileList();
            updateSubmitButton();
        }

        function updateFileList() {
            const fileList = document.getElementById('fileList');
            const fileInfo = document.getElementById('fileInfo');

            if (selectedFiles.length === 0) {
                fileInfo.style.display = 'none';
                return;
            }

            fileInfo.style.display = 'block';
            fileList.innerHTML = '';

            selectedFiles.forEach((file, index) => {
                const fileItem = document.createElement('div');
                fileItem.className = 'file-item';
                fileItem.innerHTML = `
                    <div>
                        <div class="file-name">${file.name}</div>
                        <div class="file-size">${formatFileSize(file.size)}</div>
                    </div>
                    <button class="remove-file" onclick="removeFile(${index})">Remove</button>
                `;
                fileList.appendChild(fileItem);
            });
        }

        function updateSubmitButton() {
            const submitBtn = document.getElementById('submitBtn');
            const buttonText = document.getElementById('buttonText');

            if (selectedFiles.length === 0) {
                submitBtn.disabled = true;
                buttonText.textContent = 'Select PDF files first';
            } else {
                submitBtn.disabled = false;
                buttonText.textContent = `Start Quality Check (${selectedFiles.length} files)`;
            }
        }

        async function startQualityCheck() {
            if (selectedFiles.length === 0 || isProcessing) {
                return;
            }

            isProcessing = true;
            qualityCheckResults = [];

            // Get quality criteria
            const criteria = document.getElementById('criteriaInput').value.trim();
            if (!criteria) {
                showError('Please enter quality check criteria before starting the analysis.');
                isProcessing = false;
                return;
            }

            // Show progress section
            document.getElementById('progressSection').style.display = 'block';
            document.getElementById('resultsSection').style.display = 'none';

            // Update button state
            const submitBtn = document.getElementById('submitBtn');
            const buttonText = document.getElementById('buttonText');
            const loadingSpinner = document.getElementById('loadingSpinner');

            submitBtn.disabled = true;
            buttonText.textContent = 'Processing...';
            loadingSpinner.style.display = 'inline-block';

            try {
                for (let i = 0; i < selectedFiles.length; i++) {
                    const file = selectedFiles[i];

                    // Update progress
                    const progress = ((i) / selectedFiles.length) * 100;
                    document.getElementById('progressFill').style.width = `${progress}%`;
                    document.getElementById('progressText').textContent = `Processing file ${i + 1} of ${selectedFiles.length}`;
                    document.getElementById('currentFileStatus').textContent = `Analyzing: ${file.name}`;

                    // Process the file
                    const result = await processFileForQualityCheck(file, criteria);
                    qualityCheckResults.push(result);
                }

                // Complete progress
                document.getElementById('progressFill').style.width = '100%';
                document.getElementById('progressText').textContent = 'Quality check completed!';
                document.getElementById('currentFileStatus').textContent = '';

                // Show results
                displayResults();

                showSuccess(`Quality check completed successfully! Analyzed ${selectedFiles.length} files.`);

            } catch (error) {
                showError('Error during quality check: ' + error.message);
                console.error('Quality check error:', error);
            } finally {
                // Reset button state
                isProcessing = false;
                submitBtn.disabled = false;
                buttonText.textContent = `Start Quality Check (${selectedFiles.length} files)`;
                loadingSpinner.style.display = 'none';

                // Hide progress section after a delay
                setTimeout(() => {
                    document.getElementById('progressSection').style.display = 'none';
                }, 2000);
            }
        }

        async function processFileForQualityCheck(file, criteria) {
            try {
                // Convert PDF to base64
                const base64Data = await fileToBase64(file);

                // Create the prompt for quality check
                const prompt = createQualityCheckPrompt(file.name, criteria);

                // Call Gemini API
                const response = await callGeminiAPI(prompt, base64Data);

                return {
                    fileName: file.name,
                    fileSize: file.size,
                    timestamp: new Date().toISOString(),
                    analysis: response,
                    status: 'completed'
                };

            } catch (error) {
                console.error(`Error processing file ${file.name}:`, error);
                return {
                    fileName: file.name,
                    fileSize: file.size,
                    timestamp: new Date().toISOString(),
                    analysis: `Error processing file: ${error.message}`,
                    status: 'error'
                };
            }
        }

        function createQualityCheckPrompt(fileName, criteria) {
            return `You are an AI quality assurance specialist for job description documents. Please check this PDF document ONLY against the specific criteria provided below.

DOCUMENT FILENAME: ${fileName}

SPECIFIC CRITERIA TO CHECK:
${criteria}

Please analyze the document and check it against ONLY the criteria listed above. For each criterion, state clearly whether it PASSES or FAILS, and provide specific details about any issues found.

Use this format:

OVERALL COMPLIANCE SCORE: [Score from 1-10 based on how many criteria are met]

DETAILED ANALYSIS:

${criteria.split('\n').map((criterion, index) => {
    if (criterion.trim()) {
        return `CRITERION ${index + 1}: ${criterion.trim()}
STATUS: [PASS/FAIL]
DETAILS: [Specific explanation of why it passes or fails]
`;
    }
    return '';
}).join('\n')}

SUMMARY OF ISSUES FOUND:
- [List only the specific issues related to the criteria above]
- [Each issue should be a clear, direct statement]

RECOMMENDATIONS:
- [Provide specific recommendations to address the failed criteria]
- [Focus only on fixing the issues identified above]

Please focus ONLY on the specific criteria provided. Do not analyze other aspects of the document unless they are explicitly mentioned in the criteria list.`;
        }

        async function callGeminiAPI(prompt, base64Data) {
            const requestBody = {
                contents: [{
                    parts: [
                        {
                            text: prompt
                        },
                        {
                            inline_data: {
                                mime_type: "application/pdf",
                                data: base64Data
                            }
                        }
                    ]
                }],
                generationConfig: {
                    temperature: 0.1,
                    topK: 32,
                    topP: 1,
                    maxOutputTokens: 4096,
                }
            };

            const response = await fetch(`${GEMINI_API_URL}?key=${GEMINI_API_KEY}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(requestBody)
            });

            if (!response.ok) {
                throw new Error(`API request failed: ${response.status} ${response.statusText}`);
            }

            const data = await response.json();

            if (data.candidates && data.candidates[0] && data.candidates[0].content) {
                return data.candidates[0].content.parts[0].text;
            } else {
                throw new Error('Invalid response format from Gemini API');
            }
        }

        function displayResults() {
            const resultsSection = document.getElementById('resultsSection');
            const summaryStats = document.getElementById('summaryStats');
            const qualityResults = document.getElementById('qualityResults');

            // Calculate summary statistics
            const totalFiles = qualityCheckResults.length;
            const completedFiles = qualityCheckResults.filter(r => r.status === 'completed').length;
            const errorFiles = qualityCheckResults.filter(r => r.status === 'error').length;

            // Display summary
            summaryStats.innerHTML = `
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin-bottom: 20px;">
                    <div style="background: #e8f5e8; padding: 15px; border-radius: 8px; text-align: center;">
                        <h4 style="margin: 0; color: #2e7d32;">Total Files</h4>
                        <div style="font-size: 2em; font-weight: bold; color: #1b5e20;">${totalFiles}</div>
                    </div>
                    <div style="background: #e3f2fd; padding: 15px; border-radius: 8px; text-align: center;">
                        <h4 style="margin: 0; color: #1976d2;">Completed</h4>
                        <div style="font-size: 2em; font-weight: bold; color: #0d47a1;">${completedFiles}</div>
                    </div>
                    <div style="background: #ffebee; padding: 15px; border-radius: 8px; text-align: center;">
                        <h4 style="margin: 0; color: #d32f2f;">Errors</h4>
                        <div style="font-size: 2em; font-weight: bold; color: #b71c1c;">${errorFiles}</div>
                    </div>
                </div>
            `;

            // Display individual results
            qualityResults.innerHTML = '';

            qualityCheckResults.forEach((result, index) => {
                const resultDiv = document.createElement('div');
                resultDiv.style.marginBottom = '30px';
                resultDiv.style.border = '1px solid #ddd';
                resultDiv.style.borderRadius = '8px';
                resultDiv.style.padding = '20px';
                resultDiv.style.backgroundColor = '#fafafa';

                if (result.status === 'error') {
                    resultDiv.innerHTML = `
                        <h4 style="color: #d32f2f; margin-top: 0;">❌ ${result.fileName}</h4>
                        <p style="color: #666;">File Size: ${formatFileSize(result.fileSize)}</p>
                        <div class="flaw-item critical">
                            <div class="flaw-title">Processing Error</div>
                            <div class="flaw-description">${result.analysis}</div>
                        </div>
                    `;
                } else {
                    // Display the readable analysis format
                    resultDiv.innerHTML = generateReadableResultHTML(result.fileName, result.fileSize, result.analysis);
                }

                qualityResults.appendChild(resultDiv);
            });

            resultsSection.style.display = 'block';
        }

        function generateReadableResultHTML(fileName, fileSize, analysisText) {
            // Extract overall score from the analysis text
            const scoreMatch = analysisText.match(/OVERALL COMPLIANCE SCORE:\s*(\d+)/i);
            const overallScore = scoreMatch ? scoreMatch[1] : 'N/A';
            const scoreColor = getScoreColor(overallScore);

            let html = `
                <h4 style="color: #2e7d32; margin-top: 0;">📄 ${fileName}</h4>
                <p style="color: #666;">File Size: ${formatFileSize(fileSize)} | Compliance Score: <span style="color: ${scoreColor}; font-weight: bold;">${overallScore}/10</span></p>
            `;

            // Parse and display detailed analysis section
            const detailedAnalysisMatch = analysisText.match(/DETAILED ANALYSIS:(.*?)(?=SUMMARY OF ISSUES FOUND:|RECOMMENDATIONS:|$)/is);
            if (detailedAnalysisMatch && detailedAnalysisMatch[1]) {
                const detailedContent = detailedAnalysisMatch[1].trim();
                html += `
                    <div style="margin: 15px 0;">
                        <h5 style="color: #333; margin-bottom: 10px;">📋 Criteria Analysis</h5>
                        <div style="background: #f8f9fa; padding: 15px; border-radius: 5px; border: 1px solid #dee2e6;">
                            ${formatCriteriaAnalysis(detailedContent)}
                        </div>
                    </div>
                `;
            }

            // Parse and display issues found
            const issuesMatch = analysisText.match(/SUMMARY OF ISSUES FOUND:(.*?)(?=RECOMMENDATIONS:|$)/is);
            if (issuesMatch && issuesMatch[1]) {
                const issuesContent = issuesMatch[1].trim();
                if (issuesContent && issuesContent !== '-' && issuesContent !== 'None' && issuesContent !== 'N/A') {
                    html += `
                        <div style="margin: 15px 0;">
                            <h5 style="color: #333; margin-bottom: 10px;">⚠️ Issues Found</h5>
                            <div style="background: #fff3e0; padding: 15px; border-radius: 5px; border-left: 4px solid #ff9800;">
                                ${formatBulletPoints(issuesContent)}
                            </div>
                        </div>
                    `;
                }
            }

            // Parse and display recommendations
            const recommendationsMatch = analysisText.match(/RECOMMENDATIONS:(.*?)$/is);
            if (recommendationsMatch && recommendationsMatch[1]) {
                const recommendationsContent = recommendationsMatch[1].trim();
                if (recommendationsContent && recommendationsContent !== '-' && recommendationsContent !== 'None' && recommendationsContent !== 'N/A') {
                    html += `
                        <div style="margin: 15px 0;">
                            <h5 style="color: #333; margin-bottom: 10px;">� Recommendations</h5>
                            <div style="background: #e8f5e8; padding: 15px; border-radius: 5px; border-left: 4px solid #4caf50;">
                                ${formatBulletPoints(recommendationsContent)}
                            </div>
                        </div>
                    `;
                }
            }

            return html;
        }

        function formatCriteriaAnalysis(content) {
            // Parse criteria analysis with CRITERION, STATUS, and DETAILS
            const criteriaBlocks = content.split(/CRITERION \d+:/i).filter(block => block.trim());
            let formattedContent = '';

            criteriaBlocks.forEach((block, index) => {
                const lines = block.trim().split('\n').map(line => line.trim()).filter(line => line);
                if (lines.length === 0) return;

                let criterionText = '';
                let status = '';
                let details = '';

                // Parse the block
                lines.forEach(line => {
                    if (line.startsWith('STATUS:')) {
                        status = line.replace('STATUS:', '').trim();
                    } else if (line.startsWith('DETAILS:')) {
                        details = line.replace('DETAILS:', '').trim();
                    } else if (!line.startsWith('STATUS:') && !line.startsWith('DETAILS:')) {
                        criterionText += line + ' ';
                    }
                });

                criterionText = criterionText.trim();

                if (criterionText) {
                    const isPass = status.toLowerCase().includes('pass');
                    const statusIcon = isPass ? '✅' : '❌';
                    const statusColor = isPass ? '#4caf50' : '#f44336';
                    const bgColor = isPass ? '#e8f5e8' : '#ffebee';

                    formattedContent += `
                        <div style="margin: 15px 0; padding: 15px; border-radius: 8px; background: ${bgColor}; border-left: 4px solid ${statusColor};">
                            <div style="font-weight: bold; margin-bottom: 8px; color: #333;">
                                ${statusIcon} ${criterionText}
                            </div>
                            <div style="margin-bottom: 5px;">
                                <strong>Status:</strong> <span style="color: ${statusColor}; font-weight: bold;">${status}</span>
                            </div>
                            ${details ? `<div style="color: #666; font-size: 0.95em;">${details}</div>` : ''}
                        </div>
                    `;
                }
            });

            return formattedContent || '<div style="color: #666; font-style: italic;">No criteria analysis available.</div>';
        }

        function formatBulletPoints(content) {
            // Convert bullet points and line breaks to HTML
            const lines = content.split('\n').map(line => line.trim()).filter(line => line);
            let formattedContent = '';

            lines.forEach(line => {
                if (line.startsWith('-') || line.startsWith('•')) {
                    // Remove the bullet and format as list item
                    const cleanLine = line.substring(1).trim();
                    if (cleanLine) {
                        formattedContent += `<div style="margin: 8px 0; padding-left: 20px; position: relative;">
                            <span style="position: absolute; left: 0; color: #2e7d32; font-weight: bold;">•</span>
                            ${cleanLine}
                        </div>`;
                    }
                } else if (line) {
                    // Regular line
                    formattedContent += `<div style="margin: 8px 0;">${line}</div>`;
                }
            });

            return formattedContent || '<div style="color: #666; font-style: italic;">No issues found in this category.</div>';
        }

        function formatComplianceStatus(content) {
            const lines = content.split('\n').map(line => line.trim()).filter(line => line);
            let formattedContent = '';

            lines.forEach(line => {
                if (line.includes(':')) {
                    const [label, status] = line.split(':').map(part => part.trim());
                    const isPass = status.toLowerCase().includes('pass');
                    const statusIcon = isPass ? '✅' : '❌';
                    const statusColor = isPass ? '#4caf50' : '#f44336';

                    formattedContent += `
                        <div style="margin: 8px 0; display: flex; justify-content: space-between; align-items: center;">
                            <strong>${label}:</strong>
                            <span style="color: ${statusColor}; font-weight: bold;">
                                ${statusIcon} ${status}
                            </span>
                        </div>
                    `;
                } else if (line) {
                    formattedContent += `<div style="margin: 8px 0;">${line}</div>`;
                }
            });

            return formattedContent || '<div style="color: #666; font-style: italic;">No compliance information available.</div>';
        }

        function getScoreColor(score) {
            const numScore = parseInt(score);
            if (numScore >= 8) return '#4caf50';
            if (numScore >= 6) return '#ff9800';
            if (numScore >= 4) return '#f44336';
            return '#9e9e9e';
        }

        function getSeverityIcon(severity) {
            switch (severity) {
                case 'critical': return '🚨';
                case 'warning': return '⚠️';
                case 'info': return 'ℹ️';
                default: return '•';
            }
        }

        async function fileToBase64(file) {
            return new Promise((resolve, reject) => {
                const reader = new FileReader();
                reader.onload = () => {
                    const base64 = reader.result.split(',')[1];
                    resolve(base64);
                };
                reader.onerror = reject;
                reader.readAsDataURL(file);
            });
        }

        function copyResults() {
            const resultsText = generateResultsText();

            navigator.clipboard.writeText(resultsText).then(() => {
                const copyBtn = document.getElementById('copyBtn');
                copyBtn.classList.add('copied');
                copyBtn.textContent = 'Copied to Clipboard ✓';

                setTimeout(() => {
                    copyBtn.classList.remove('copied');
                    copyBtn.textContent = 'Copy Results to Clipboard';
                }, 60000); // Reset after 1 minute
            }).catch(err => {
                showError('Failed to copy to clipboard: ' + err.message);
            });
        }

        function generateResultsText() {
            let text = `PAIP - AI Quality Check Results\n`;
            text += `Generated: ${new Date().toLocaleString()}\n`;
            text += `Total Files Analyzed: ${qualityCheckResults.length}\n\n`;

            qualityCheckResults.forEach((result, index) => {
                text += `\n${'='.repeat(60)}\n`;
                text += `FILE ${index + 1}: ${result.fileName}\n`;
                text += `${'='.repeat(60)}\n`;

                if (result.status === 'error') {
                    text += `Status: ERROR\n`;
                    text += `Error: ${result.analysis}\n`;
                } else {
                    // Extract overall compliance score
                    const scoreMatch = result.analysis.match(/OVERALL COMPLIANCE SCORE:\s*(\d+)/i);
                    const overallScore = scoreMatch ? scoreMatch[1] : 'N/A';
                    text += `Compliance Score: ${overallScore}/10\n\n`;

                    // Add the full readable analysis
                    text += result.analysis;
                    text += `\n`;
                }
            });

            return text;
        }

        // Utility functions
        function formatFileSize(bytes) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }

        function showError(message) {
            // Remove existing messages
            const existingMessages = document.querySelectorAll('.error, .success');
            existingMessages.forEach(msg => msg.remove());

            const errorDiv = document.createElement('div');
            errorDiv.className = 'error';
            errorDiv.textContent = message;
            document.querySelector('.container').insertBefore(errorDiv, document.querySelector('.section'));

            // Auto-remove after 5 seconds
            setTimeout(() => {
                if (errorDiv.parentNode) {
                    errorDiv.remove();
                }
            }, 5000);
        }

        function showSuccess(message) {
            // Remove existing messages
            const existingMessages = document.querySelectorAll('.error, .success');
            existingMessages.forEach(msg => msg.remove());

            const successDiv = document.createElement('div');
            successDiv.className = 'success';
            successDiv.textContent = message;
            document.querySelector('.container').insertBefore(successDiv, document.querySelector('.section'));

            // Auto-remove after 3 seconds
            setTimeout(() => {
                if (successDiv.parentNode) {
                    successDiv.remove();
                }
            }, 3000);
        }

        // Initialize the application
        document.addEventListener('DOMContentLoaded', function() {
            console.log('PAIP AI Quality Check System - Ready');

            // Set focus to criteria textarea for immediate use
            setTimeout(() => {
                document.getElementById('criteriaInput').focus();
            }, 500);
        });
    </script>
    </div> <!-- End main-content -->
</body>
</html>