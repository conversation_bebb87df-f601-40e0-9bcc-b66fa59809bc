<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PAIP - Positions AI Processing (OpenAI)</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

        :root {
            --primary-green: #2e7d32;
            --light-green: #4caf50;
            --accent-green: #66bb6a;
            --secondary-blue: #1976d2;
            --accent-orange: #ff9800;
            --white: #ffffff;
            --light-gray: #f5f5f5;
            --medium-gray: #e0e0e0;
            --dark-gray: #333333;
            --text-gray: #666666;
            --shadow-light: 0 2px 8px rgba(0,0,0,0.1);
            --shadow-medium: 0 4px 12px rgba(0,0,0,0.15);
            --border-radius: 8px;
            --border-radius-small: 6px;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: var(--light-gray);
            min-height: 100vh;
            padding: 20px;
            margin: 0;
        }

        /* Navigation Styles */
        .navbar {
            background: var(--white);
            box-shadow: var(--shadow-light);
            border-bottom: 1px solid var(--medium-gray);
            position: sticky;
            top: 0;
            z-index: 1000;
            margin: -20px -20px 20px -20px;
        }

        .nav-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            height: 70px;
        }

        .nav-brand {
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--primary-green);
            text-decoration: none;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .nav-brand:hover {
            color: var(--light-green);
        }

        .nav-menu {
            display: flex;
            list-style: none;
            gap: 0;
            margin: 0;
            padding: 0;
        }

        .nav-item {
            position: relative;
        }

        .nav-link {
            display: block;
            padding: 15px 20px;
            color: var(--dark-gray);
            text-decoration: none;
            font-weight: 500;
            transition: all 0.3s ease;
            border-radius: var(--border-radius-small);
            margin: 0 5px;
        }

        .nav-link:hover {
            color: var(--primary-green);
            background: rgba(46, 125, 50, 0.1);
        }

        .nav-link.active {
            color: var(--primary-green);
            background: rgba(46, 125, 50, 0.15);
            font-weight: 600;
        }

        .hamburger {
            display: none;
            flex-direction: column;
            cursor: pointer;
            padding: 5px;
            border-radius: var(--border-radius-small);
            transition: background 0.3s ease;
        }

        .hamburger:hover {
            background: rgba(46, 125, 50, 0.1);
        }

        .hamburger span {
            width: 25px;
            height: 3px;
            background: var(--primary-green);
            margin: 3px 0;
            transition: 0.3s;
            border-radius: 2px;
        }

        .hamburger.active span:nth-child(1) {
            transform: rotate(-45deg) translate(-5px, 6px);
        }

        .hamburger.active span:nth-child(2) {
            opacity: 0;
        }

        .hamburger.active span:nth-child(3) {
            transform: rotate(45deg) translate(-5px, -6px);
        }

        /* Mobile Styles */
        @media (max-width: 768px) {
            .nav-menu {
                position: fixed;
                left: -100%;
                top: 70px;
                flex-direction: column;
                background: var(--white);
                width: 100%;
                text-align: center;
                transition: 0.3s;
                box-shadow: var(--shadow-medium);
                border-top: 1px solid var(--medium-gray);
                padding: 20px 0;
            }

            .nav-menu.active {
                left: 0;
            }

            .nav-item {
                margin: 5px 0;
            }

            .nav-link {
                padding: 15px 20px;
                margin: 0 20px;
                border-radius: var(--border-radius);
            }

            .hamburger {
                display: flex;
            }
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: var(--white);
            border-radius: var(--border-radius);
            padding: 40px;
            box-shadow: var(--shadow-light);
            border: 1px solid var(--medium-gray);
        }

        h1 {
            color: var(--primary-green);
            text-align: center;
            font-size: 2.5rem;
            font-weight: 600;
            margin-bottom: 10px;
        }

        .subtitle {
            text-align: center;
            color: var(--text-gray);
            font-size: 1.1rem;
            margin-bottom: 40px;
        }

        .section-card {
            background: var(--white);
            border-radius: var(--border-radius);
            padding: 25px;
            margin-bottom: 25px;
            box-shadow: var(--shadow-light);
            border: 1px solid var(--medium-gray);
            transition: box-shadow 0.2s ease;
        }

        .section-card:hover {
            box-shadow: var(--shadow-medium);
        }

        .position-codes-section {
            border-left: 4px solid var(--light-green);
        }

        .upload-section {
            border: 2px dashed var(--light-green);
            text-align: center;
            transition: border-color 0.2s ease, background-color 0.2s ease;
        }

        .upload-section:hover {
            border-color: var(--primary-green);
            background-color: rgba(76, 175, 80, 0.05);
        }

        .upload-section.dragover {
            border-color: var(--primary-green);
            background-color: rgba(76, 175, 80, 0.1);
        }

        #fileInput {
            display: none;
        }

        .section-title {
            font-size: 1.4rem;
            font-weight: 600;
            margin-bottom: 15px;
            color: var(--primary-green);
        }

        .btn {
            border: none;
            border-radius: var(--border-radius-small);
            cursor: pointer;
            font-family: inherit;
            font-weight: 500;
            transition: all 0.2s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
        }

        .btn:hover {
            transform: translateY(-1px);
        }

        .btn:active {
            transform: translateY(0);
        }

        .upload-btn {
            background: var(--light-green);
            color: white;
            padding: 12px 24px;
            font-size: 16px;
            margin: 10px;
            box-shadow: var(--shadow-light);
        }

        .upload-btn:hover {
            background: var(--primary-green);
            box-shadow: var(--shadow-medium);
        }

        .process-btn {
            background: var(--primary-green);
            color: white;
            padding: 16px 32px;
            font-size: 18px;
            width: 100%;
            margin-top: 25px;
            box-shadow: var(--shadow-light);
        }

        .process-btn:hover {
            background: var(--light-green);
            box-shadow: var(--shadow-medium);
        }

        .process-btn:disabled {
            background: var(--medium-gray);
            color: var(--text-gray);
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }

        .copy-btn {
            background: var(--accent-orange);
            color: white;
            padding: 12px 24px;
            font-size: 14px;
            margin: 15px 0;
            min-width: 200px;
            box-shadow: var(--shadow-light);
            transition: all 0.4s ease;
        }

        .copy-btn:hover {
            background: #f57c00;
            box-shadow: var(--shadow-medium);
        }

        .copy-btn.copied {
            background: var(--primary-green);
            transition: all 60s ease;
        }

        .copy-btn.copied:hover {
            background: var(--primary-green);
        }

        .copy-btn .checkmark {
            display: none;
        }

        .copy-btn.copied .checkmark {
            display: inline;
        }

        .copy-btn.copied .copy-text {
            display: none;
        }

        .copy-btn .copied-text {
            display: none;
        }

        .copy-btn.copied .copied-text {
            display: inline;
        }

        .results-controls {
            display: flex;
            gap: 15px;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }

        .download-btn {
            background: var(--secondary-blue);
            color: white;
            padding: 12px 24px;
            font-size: 14px;
            min-width: 200px;
            box-shadow: var(--shadow-light);
            transition: all 0.3s ease;
        }

        .download-btn:hover {
            background: #1565c0;
            box-shadow: var(--shadow-medium);
        }

        .reset-btn {
            background: var(--text-gray);
            color: white;
            padding: 10px 20px;
            font-size: 14px;
            margin: 10px;
            box-shadow: var(--shadow-light);
        }

        .reset-btn:hover {
            background: var(--dark-gray);
            box-shadow: var(--shadow-medium);
        }

        .input-field {
            width: 100%;
            padding: 12px 16px;
            border: 2px solid var(--medium-gray);
            border-radius: var(--border-radius-small);
            font-size: 16px;
            font-family: inherit;
            background: var(--white);
            transition: border-color 0.2s ease;
            margin-bottom: 15px;
        }

        .input-field:focus {
            outline: none;
            border-color: var(--light-green);
            box-shadow: 0 0 0 3px rgba(76, 175, 80, 0.1);
        }

        .input-field::placeholder {
            color: var(--text-gray);
        }

        .file-list {
            margin-top: 25px;
            display: none;
        }

        .file-item {
            background: var(--white);
            border: 1px solid var(--medium-gray);
            border-radius: var(--border-radius-small);
            padding: 20px;
            margin-bottom: 15px;
            transition: box-shadow 0.2s ease;
        }

        .file-item:hover {
            box-shadow: var(--shadow-medium);
        }

        .file-name {
            font-weight: 600;
            font-size: 16px;
            margin-bottom: 8px;
            color: var(--dark-gray);
        }

        .file-status {
            font-size: 14px;
            font-weight: 500;
            padding: 4px 12px;
            border-radius: 15px;
            display: inline-block;
            margin-bottom: 10px;
        }

        .file-status.processing {
            background: var(--accent-orange);
            color: white;
        }

        .file-status.completed {
            background: var(--primary-green);
            color: white;
        }

        .file-status.error {
            background: #f44336;
            color: white;
        }

        .progress-bar {
            width: 100%;
            height: 6px;
            background: var(--medium-gray);
            border-radius: 3px;
            overflow: hidden;
            margin: 12px 0;
        }

        .progress-fill {
            height: 100%;
            background: var(--light-green);
            width: 0%;
            transition: width 0.3s ease;
            border-radius: 3px;
        }

        .extracted-text-preview {
            background: var(--light-gray);
            border: 1px solid var(--medium-gray);
            border-radius: var(--border-radius-small);
            padding: 15px;
            margin-top: 15px;
            max-height: 150px;
            overflow-y: auto;
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            font-size: 12px;
            line-height: 1.5;
        }

        .show-more-btn {
            background: var(--text-gray);
            color: white;
            padding: 6px 12px;
            border: none;
            border-radius: 12px;
            cursor: pointer;
            font-size: 11px;
            margin-top: 8px;
            transition: background-color 0.2s ease;
        }

        .show-more-btn:hover {
            background: var(--dark-gray);
        }

        .results-section {
            margin-top: 30px;
            display: none;
        }

        .csv-output {
            background: var(--light-gray);
            border: 1px solid var(--medium-gray);
            border-radius: var(--border-radius-small);
            padding: 20px;
            max-height: 600px;
            overflow-y: auto;
            white-space: pre-wrap;
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            line-height: 1.5;
            font-size: 13px;
        }

        .csv-output::-webkit-scrollbar {
            width: 8px;
        }

        .csv-output::-webkit-scrollbar-track {
            background: var(--medium-gray);
            border-radius: 4px;
        }

        .csv-output::-webkit-scrollbar-thumb {
            background: var(--light-green);
            border-radius: 4px;
        }

        .notification {
            padding: 15px 20px;
            border-radius: var(--border-radius-small);
            margin: 15px 0;
            border-left: 4px solid;
        }

        .notification.success {
            background: rgba(76, 175, 80, 0.1);
            color: var(--primary-green);
            border-left-color: var(--primary-green);
        }

        .notification.error {
            background: rgba(244, 67, 54, 0.1);
            color: #d32f2f;
            border-left-color: #f44336;
        }

        .notification.warning {
            background: rgba(255, 152, 0, 0.1);
            color: #f57c00;
            border-left-color: #ff9800;
        }

        .notification.info {
            background: rgba(33, 150, 243, 0.1);
            color: var(--secondary-blue);
            border-left-color: var(--secondary-blue);
        }

        .spinner {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            border-top-color: #fff;
            animation: spin 1s ease-in-out infinite;
            margin-left: 10px;
            vertical-align: middle;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }

        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.7);
            display: none;
            justify-content: center;
            align-items: center;
            z-index: 1000;
        }

        .loading-content {
            background: var(--white);
            padding: 40px;
            border-radius: var(--border-radius);
            text-align: center;
            max-width: 400px;
            box-shadow: var(--shadow-medium);
        }

        .loading-spinner {
            width: 50px;
            height: 50px;
            border: 4px solid var(--medium-gray);
            border-top: 4px solid var(--primary-green);
            border-radius: 50%;
            animation: loadingSpin 1s linear infinite;
            margin: 0 auto 20px;
        }

        @keyframes loadingSpin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .loading-title {
            font-size: 1.4rem;
            font-weight: 600;
            margin-bottom: 10px;
            color: var(--primary-green);
        }

        .loading-message {
            color: var(--text-gray);
            line-height: 1.5;
        }

        .position-preview {
            background: rgba(76, 175, 80, 0.1);
            border: 1px solid rgba(76, 175, 80, 0.3);
            border-radius: var(--border-radius-small);
            padding: 12px 16px;
            margin-top: 10px;
            font-size: 14px;
        }

        /* Footer Styling */
        .footer {
            background: linear-gradient(135deg, var(--primary-green) 0%, var(--light-green) 100%);
            color: white;
            text-align: center;
            padding: 30px 20px;
            margin-top: 50px;
            border-radius: var(--border-radius) var(--border-radius) 0 0;
            position: relative;
            overflow: hidden;
        }

        .footer::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 1px;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
        }

        .footer-content {
            max-width: 1200px;
            margin: 0 auto;
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 15px;
        }

        .footer-brand {
            font-size: 1.2rem;
            font-weight: 600;
            margin-bottom: 5px;
        }

        .footer-tagline {
            font-size: 0.9rem;
            opacity: 0.9;
            margin-bottom: 10px;
        }

        .footer-links {
            display: flex;
            gap: 20px;
            margin-bottom: 15px;
        }

        .footer-link {
            color: white;
            text-decoration: none;
            font-size: 0.9rem;
            opacity: 0.8;
            transition: opacity 0.2s ease;
        }

        .footer-link:hover {
            opacity: 1;
            text-decoration: underline;
        }

        .footer-copyright {
            font-size: 0.85rem;
            opacity: 0.7;
            border-top: 1px solid rgba(255,255,255,0.2);
            padding-top: 15px;
            width: 100%;
        }

        .footer-powered {
            font-size: 0.8rem;
            opacity: 0.6;
            font-style: italic;
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .container {
                padding: 20px;
                margin: 10px;
            }

            h1 {
                font-size: 2rem;
            }

            .section-card {
                padding: 20px;
            }

            .btn {
                padding: 12px 20px;
                font-size: 14px;
            }

            .footer {
                margin-top: 30px;
                padding: 25px 15px;
            }

            .footer-links {
                flex-direction: column;
                gap: 10px;
            }

            .footer-brand {
                font-size: 1.1rem;
            }
        }
    </style>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.11.174/pdf.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/marked/9.1.2/marked.min.js"></script>
</head>
<body>
    <!-- Navigation Menu -->
    <nav class="navbar">
        <div class="nav-container">
            <a href="index.html" class="nav-brand">
                🎯 PAIP
            </a>
            <ul class="nav-menu" id="nav-menu">
                <li class="nav-item">
                    <a href="index.html" class="nav-link">🏠 Home</a>
                </li>
                <li class="nav-item">
                    <a href="gemini_file_upload_v3.html" class="nav-link">📄 Document Analysis</a>
                </li>
                <li class="nav-item">
                    <a href="openai_format_post.html" class="nav-link active">📊 PDF to CSV (OpenAI)</a>
                </li>
                <li class="nav-item">
                    <a href="paip_qc.html" class="nav-link">🔍 Quality Check</a>
                </li>
                <li class="nav-item">
                    <a href="html_markdownviewer.html" class="nav-link">📝 Markdown Viewer</a>
                </li>
            </ul>
            <div class="hamburger" id="hamburger">
                <span></span>
                <span></span>
                <span></span>
            </div>
        </div>
    </nav>

    <div class="container">
        <h1>PAIP - Positions AI Processing (OpenAI)</h1>
        <p class="subtitle">
            🚀 Upload PDF files containing job position information to extract and format data into CSV format using OpenAI GPT-4o<br>
            📁 Upload 6 files: 5 job description files and 1 advertisement file
        </p>

        <!-- Position Reference Codes Section -->
        <div class="section-card position-codes-section">
            <h3 class="section-title">🎯 Position Reference Codes</h3>
            <p style="color: var(--dark-gray); margin-bottom: 15px; opacity: 0.8;">
                Enter the position reference codes you want to extract (separated by commas)<br>
                <small style="color: var(--primary-blue);">💡 Example: ESPPM 004, ESPPM 005, ESPPM 008, PS19-001, PS12-002</small>
            </p>
            <input type="text" id="positionCodes" class="input-field"
                   placeholder="Enter position codes separated by commas (e.g., ESPPM 004, ESPPM 005, ESPPM 008)"
                   oninput="validatePositionCodesInput()">
            <div style="font-size: 12px; color: var(--dark-gray); opacity: 0.7;">
                <strong>📝 Note:</strong> Leave empty to extract all positions found in the documents
            </div>
            <div id="positionCodesPreview" class="position-preview" style="display: none;"></div>
        </div>

        <!-- Upload Section -->
        <div class="section-card upload-section" id="uploadSection">
            <h3 class="section-title">📤 Upload PDF Files</h3>
            <div style="font-size: 48px; margin-bottom: 20px; opacity: 0.6;">📄</div>
            <p style="color: var(--dark-gray); margin-bottom: 20px;">
                Drag and drop PDF files here or click to select multiple files
            </p>
            <input type="file" id="fileInput" accept=".pdf" multiple />
            <button class="btn upload-btn" onclick="document.getElementById('fileInput').click()">
                <span>📁</span>
                Choose PDF Files
            </button>
        </div>

        <!-- File List Section -->
        <div class="section-card file-list" id="fileList">
            <h3 class="section-title">📋 Uploaded Files</h3>
            <div id="fileItems"></div>
        </div>

        <!-- Process Button -->
        <button class="btn process-btn" id="processBtn" onclick="processAllData()" disabled>
            <span>🤖</span>
            <span id="processButtonText">Upload PDF files first</span>
            <span id="processSpinner" class="spinner" style="display: none;"></span>
        </button>

        <!-- Reset Button -->
        <button class="btn reset-btn" onclick="resetApplication()">
            <span>🔄</span>
            Reset All
        </button>

        <!-- Results Section -->
        <div class="section-card results-section" id="resultsSection">
            <h3 class="section-title">📊 CSV Output</h3>
            <div class="results-controls">
                <button class="btn copy-btn" id="copyBtn" onclick="copyToClipboard()">
                    <span class="checkmark">✅</span>
                    <span class="copy-text">📋 Copy CSV to Clipboard</span>
                    <span class="copied-text">✅ Copied Successfully!</span>
                </button>
                <button class="btn download-btn" id="downloadBtn" onclick="downloadCSV()">
                    <span>💾</span>
                    Download CSV File
                </button>
            </div>
            <div class="csv-output" id="csvOutput"></div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="footer">
        <div class="footer-content">
            <div class="footer-brand">PAIP - Positions AI Processing</div>
            <div class="footer-tagline">Intelligent PDF Processing for Job Position Analysis</div>

            <div class="footer-links">
                <a href="#" class="footer-link">About</a>
                <a href="#" class="footer-link">Documentation</a>
                <a href="#" class="footer-link">Support</a>
                <a href="#" class="footer-link">Privacy Policy</a>
            </div>

            <div class="footer-copyright">
                <div>© 2025 Dakoii Systems. All rights reserved.</div>
                <div class="footer-powered">Powered by Dakoii AI</div>
            </div>
        </div>
    </footer>

    <!-- Loading Overlay -->
    <div class="loading-overlay" id="loadingOverlay">
        <div class="loading-content">
            <div class="loading-spinner"></div>
            <h3 class="loading-title" id="loadingTitle">🤖 AI Processing...</h3>
            <p class="loading-message" id="loadingMessage">Please wait while we analyze your files and generate CSV data. This may take a few minutes.</p>
        </div>
    </div>

    <!-- Notification Container -->
    <div id="notificationContainer"></div>

    <script>
        // ===== CONFIGURATION =====
        const OPENAI_API_KEY = '********************************************************';
        const OPENAI_MODEL = 'gpt-4o';
        const OPENAI_API_URL = 'https://api.openai.com/v1/chat/completions';

        // ===== DATA STRUCTURE MANAGEMENT =====
        let dataStore = {
            uploadedFiles: new Map(),
            extractedTexts: new Map(),
            fileStatuses: new Map(),
            processingResults: null
        };

        // Set up PDF.js worker
        pdfjsLib.GlobalWorkerOptions.workerSrc = 'https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.11.174/pdf.worker.min.js';

        // ===== 8. DATA STRUCTURE MANAGEMENT FUNCTIONS =====

        function initializeDataStore() {
            dataStore = {
                uploadedFiles: new Map(),
                extractedTexts: new Map(),
                fileStatuses: new Map(),
                processingResults: null
            };
        }

        function updateFileStatus(filename, status) {
            dataStore.fileStatuses.set(filename, status);

            // Update UI
            const fileItem = document.querySelector(`[data-filename="${filename}"]`);
            if (fileItem) {
                const statusElement = fileItem.querySelector('.file-status');
                statusElement.textContent = status;
                statusElement.className = `file-status ${getStatusClass(status)}`;
            }

            // Check if all files are ready for processing
            updateProcessButtonState();
        }

        function getStatusClass(status) {
            if (status.includes('Error') || status.includes('Failed')) return 'error';
            if (status.includes('Completed') || status.includes('Ready')) return 'completed';
            if (status.includes('Processing') || status.includes('Extracting')) return 'processing';
            return '';
        }

        function getProcessingResults() {
            return dataStore.processingResults;
        }

        // ===== 1. FILE UPLOAD & TEXT EXTRACTION FUNCTIONS =====

        function initializeDragDrop() {
            const uploadSection = document.getElementById('uploadSection');

            uploadSection.addEventListener('dragover', handleDragOver);
            uploadSection.addEventListener('dragleave', handleDragLeave);
            uploadSection.addEventListener('drop', handleDrop);

            document.getElementById('fileInput').addEventListener('change', handleFileInputChange);
        }

        function handleDragOver(e) {
            e.preventDefault();
            document.getElementById('uploadSection').classList.add('dragover');
        }

        function handleDragLeave(e) {
            e.preventDefault();
            document.getElementById('uploadSection').classList.remove('dragover');
        }

        function handleDrop(e) {
            e.preventDefault();
            document.getElementById('uploadSection').classList.remove('dragover');

            const files = Array.from(e.dataTransfer.files).filter(file => file.type === 'application/pdf');
            if (files.length > 0) {
                handleFileUpload(files);
            } else {
                showNotification('Please select valid PDF files only.', 'error');
            }
        }

        function handleFileInputChange(e) {
            const files = Array.from(e.target.files).filter(file => file.type === 'application/pdf');
            if (files.length > 0) {
                handleFileUpload(files);
            }
        }

        function handleFileUpload(files) {
            showNotification(`Processing ${files.length} PDF file(s)...`, 'info');

            // Show file list section
            document.getElementById('fileList').style.display = 'block';

            files.forEach(file => {
                // Store file
                dataStore.uploadedFiles.set(file.name, file);
                updateFileStatus(file.name, 'Uploaded - Ready for extraction');

                // Create UI element for file
                createFileItem(file);

                // Start text extraction
                extractTextFromPDF(file, (progress) => updateProgressBar(file.name, progress));
            });
        }

        function createFileItem(file) {
            const fileItems = document.getElementById('fileItems');

            const fileItem = document.createElement('div');
            fileItem.className = 'file-item';
            fileItem.setAttribute('data-filename', file.name);

            fileItem.innerHTML = `
                <div class="file-name">${file.name}</div>
                <div class="file-status">Uploaded - Ready for extraction</div>
                <div class="progress-bar">
                    <div class="progress-fill" id="progress-${file.name.replace(/[^a-zA-Z0-9]/g, '_')}"></div>
                </div>
                <div class="extracted-text-preview" id="preview-${file.name.replace(/[^a-zA-Z0-9]/g, '_')}" style="display: none;"></div>
            `;

            fileItems.appendChild(fileItem);
        }

        async function extractTextFromPDF(file, progressCallback) {
            const filename = file.name;
            updateFileStatus(filename, 'Extracting text...');

            try {
                const arrayBuffer = await file.arrayBuffer();
                const pdf = await pdfjsLib.getDocument(arrayBuffer).promise;
                const totalPages = pdf.numPages;

                let extractedText = '';

                for (let pageNum = 1; pageNum <= totalPages; pageNum++) {
                    try {
                        const page = await pdf.getPage(pageNum);
                        const textContent = await page.getTextContent();
                        const pageText = textContent.items.map(item => item.str).join(' ');
                        extractedText += `\n\nPage ${pageNum}:\n${pageText}`;

                        // Update progress
                        const progress = (pageNum / totalPages) * 100;
                        progressCallback(progress);

                    } catch (pageError) {
                        console.error(`Error extracting page ${pageNum}:`, pageError);
                        extractedText += `\n\nPage ${pageNum}: [Error extracting text]`;
                    }
                }

                // Store extracted text
                dataStore.extractedTexts.set(filename, extractedText);
                updateFileStatus(filename, 'Text extraction completed');

                // Display preview
                displayExtractedText(filename, extractedText);

            } catch (error) {
                handleExtractionError(filename, error);
            }
        }

        function updateProgressBar(fileId, progress) {
            const progressElement = document.getElementById(`progress-${fileId.replace(/[^a-zA-Z0-9]/g, '_')}`);
            if (progressElement) {
                progressElement.style.width = `${progress}%`;
            }
        }

        function displayExtractedText(filename, text) {
            const previewId = `preview-${filename.replace(/[^a-zA-Z0-9]/g, '_')}`;
            const previewElement = document.getElementById(previewId);

            if (previewElement) {
                const preview = text.substring(0, 200) + (text.length > 200 ? '...' : '');
                previewElement.innerHTML = `
                    <div>${preview}</div>
                    ${text.length > 200 ? `<button class="show-more-btn" onclick="toggleFullText('${previewId}', '${filename}')">Show More</button>` : ''}
                `;
                previewElement.style.display = 'block';
            }
        }

        function toggleFullText(previewId, filename) {
            const previewElement = document.getElementById(previewId);
            const fullText = dataStore.extractedTexts.get(filename);

            if (previewElement.dataset.expanded === 'true') {
                const preview = fullText.substring(0, 200) + '...';
                previewElement.innerHTML = `
                    <div>${preview}</div>
                    <button class="show-more-btn" onclick="toggleFullText('${previewId}', '${filename}')">Show More</button>
                `;
                previewElement.dataset.expanded = 'false';
            } else {
                previewElement.innerHTML = `
                    <div>${fullText}</div>
                    <button class="show-more-btn" onclick="toggleFullText('${previewId}', '${filename}')">Show Less</button>
                `;
                previewElement.dataset.expanded = 'true';
            }
        }

        // ===== 2. DATA PROCESSING FUNCTIONS =====

        async function processAllData() {
            if (!validateExtractedData()) {
                return;
            }

            showLoadingState();

            try {
                // Show progress update
                showNotification('Preparing comprehensive analysis using extracted text content...', 'info');

                // Prepare AI prompt with all extracted texts
                const prompt = prepareAIPrompt(dataStore.extractedTexts);

                // Call OpenAI API with extracted texts
                showNotification(`Sending extracted texts from ${dataStore.uploadedFiles.size} PDF files to OpenAI for analysis...`, 'info');
                console.log('Calling OpenAI API...');
                const csvData = await callOpenAIWithDocuments(prompt);
                console.log('Received response from OpenAI API');

                // Format and validate response
                console.log('Formatting AI response...');
                const formattedCSV = formatAIResponse(csvData);
                console.log('AI response formatted successfully');

                // Store results
                dataStore.processingResults = formattedCSV;
                console.log('Results stored in dataStore');

                // Display output
                console.log('Displaying output...');
                displayMarkdownOutput(formattedCSV);

                hideLoadingState();
                showNotification('CSV processing completed successfully using OpenAI analysis!', 'success');

            } catch (error) {
                console.error('Error in processAllData:', error);
                hideLoadingState();
                handleAPIError(error);
            }
        }

        function validateExtractedData() {
            if (dataStore.extractedTexts.size === 0) {
                showNotification('No PDF files have been processed yet. Please upload and extract text from PDF files first.', 'error');
                return false;
            }

            // Check if all files have completed extraction
            for (let [filename, status] of dataStore.fileStatuses) {
                if (!status.includes('completed')) {
                    showNotification(`File "${filename}" is still processing. Please wait for all files to complete.`, 'error');
                    return false;
                }
            }

            // Validate position codes input
            const positionCodesInput = document.getElementById('positionCodes').value.trim();
            if (positionCodesInput) {
                const positions = positionCodesInput.split(',').map(code => code.trim()).filter(code => code.length > 0);
                if (positions.length === 0) {
                    showNotification('Please enter valid position reference codes or leave the field empty to extract all positions.', 'error');
                    return false;
                }
                showNotification(`Will extract data for ${positions.length} specific position(s): ${positions.join(', ')}`, 'info');
            } else {
                showNotification('No specific positions specified. Will extract all positions found in the documents.', 'info');
            }

            // Recommend uploading 6 files (5 JD + 1 advertisement) but don't enforce it
            if (dataStore.extractedTexts.size < 6) {
                showNotification(`You have uploaded ${dataStore.extractedTexts.size} files. For best results, upload 6 files (5 job descriptions + 1 advertisement). You can still proceed with current files.`, 'info');
            } else if (dataStore.extractedTexts.size > 6) {
                showNotification(`You have uploaded ${dataStore.extractedTexts.size} files. Expected 6 files (5 job descriptions + 1 advertisement). Processing will continue with all uploaded files.`, 'info');
            }

            return true;
        }

        function prepareAIPrompt(extractedTexts) {
            let combinedText = '';
            let jobDescriptionFiles = [];
            let advertisementFile = null;

            // Get position reference codes from input
            const positionCodesInput = document.getElementById('positionCodes').value.trim();
            const specificPositions = positionCodesInput ?
                positionCodesInput.split(',').map(code => code.trim()).filter(code => code.length > 0) :
                [];

            // Categorize files and combine texts
            for (let [filename, text] of extractedTexts) {
                combinedText += `\n\n=== FILE: ${filename} ===\n${text}`;

                // Try to identify file types based on content or filename
                if (filename.toLowerCase().includes('advertisement') ||
                    filename.toLowerCase().includes('advert') ||
                    text.toLowerCase().includes('advertisement') ||
                    text.toLowerCase().includes('vacancy announcement')) {
                    advertisementFile = filename;
                } else {
                    jobDescriptionFiles.push(filename);
                }
            }

            // Create position-specific instructions
            let positionInstructions = '';
            if (specificPositions.length > 0) {
                positionInstructions = `
SPECIFIC POSITIONS TO EXTRACT:
You must extract data ONLY for these specific position reference codes:
${specificPositions.map(pos => `- ${pos}`).join('\n')}

IMPORTANT:
- Only create CSV rows for the positions listed above
- If a specified position is not found in the documents, include it with empty values (except position_reference)
- Ignore any other positions that may be mentioned in the documents
- Match position references exactly as specified (case-insensitive)`;
            } else {
                positionInstructions = `
POSITION EXTRACTION:
- Extract ALL position references found in the documents
- Look for any position reference patterns (ESPPM XXX, PS XX-XXX, etc.)
- Create one CSV row for each unique position found`;
            }

            const prompt = `
Please analyze the extracted text content from PDF documents containing job position information and extract the data into CSV format.

IMPORTANT: You are working with extracted text content from PDF documents. Analyze this text carefully to extract all relevant job position information with high accuracy.

DOCUMENT STRUCTURE:
- You have ${extractedTexts.size} files total
- ${jobDescriptionFiles.length} appear to be job description files: ${jobDescriptionFiles.join(', ')}
- ${advertisementFile ? '1 appears to be an advertisement file: ' + advertisementFile : 'No clear advertisement file identified'}

${positionInstructions}

The CSV should contain the following columns with ALL values enclosed in quotation marks:
- "position_reference" (the exact position reference code)
- "designation" (job title/position name)
- "classification" (salary grade like PS19, PS12, etc. in the advertisment file)
- "award" (PUBC or PUB only one of these see the advertisement file to identify)
- "location" (work location/place)
- "annual_salary" (salary range in format: xxxxx - xxxxx)
- "qualifications" (educational requirements . Write more details to improve the information where necessary)
- "knowledge" (required knowledge areas.  Write more details to improve the information where necessary)
- "skills_competencies" (required skills and competencies. Write more details to improve the information where necessary)
- "job_experiences" (required work experience. Write more details to improve the information where necessary)
- "remarks" (additional notes or requirements)

Important formatting requirements:
1. ALL values must be enclosed in quotation marks
2. Use comma as field separator
3. Include proper CSV header row
4. If a field is empty or not found, use empty quotes ""
5. For multi-line content within a field, replace line breaks with semicolons
6. Escape any quotation marks within field values by doubling them

EXTRACTION RULES:
- Carefully analyze the extracted text content from the PDF documents
- Look for structured information, tables, and formatted data within the text
- If the same position appears in multiple files, consolidate the information
- Extract complete and accurate information for each position
- Maintain consistency in data formatting
- Pay attention to context clues and formatting patterns in the extracted text

EXTRACTED TEXT CONTENT:
${combinedText}

INSTRUCTIONS:
1. Analyze the extracted text content provided above
2. Identify all job position information and relevant details
3. Extract and organize the data according to the CSV format requirements
4. Ensure accuracy and completeness of the extracted information

Please provide ONLY the CSV output, no additional text or explanations.`;

            return prompt;
        }

        // ===== 3. AI PROCESSING FUNCTIONS =====

        async function callOpenAIWithDocuments(prompt) {
            console.log('Starting comprehensive document analysis...');
            console.log(`Processing extracted texts from ${dataStore.uploadedFiles.size} PDF files`);

            // Note: OpenAI doesn't support PDF files directly like Gemini
            // We'll use the extracted text content which is already included in the prompt
            showNotification(`Analyzing extracted texts from ${dataStore.uploadedFiles.size} PDF files using OpenAI...`, 'info');

            const requestBody = {
                model: OPENAI_MODEL,
                messages: [
                    {
                        role: "system",
                        content: "You are an expert data extraction specialist. Analyze the provided extracted text content from PDF documents and extract job position information into CSV format as requested. Focus on accuracy and completeness of the data extraction."
                    },
                    {
                        role: "user",
                        content: prompt
                    }
                ],
                max_tokens: 4096,
                temperature: 0.3
            };

            console.log('Sending request to OpenAI API...');
            const response = await fetch(OPENAI_API_URL, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${OPENAI_API_KEY}`
                },
                body: JSON.stringify(requestBody)
            });

            console.log('Received response from OpenAI API, status:', response.status);

            if (!response.ok) {
                const errorText = await response.text();
                console.error('API Error Response:', errorText);
                throw new Error(`OpenAI API error: ${response.status} ${response.statusText} - ${errorText}`);
            }

            const data = await response.json();
            console.log('API response data structure:', {
                hasChoices: !!data.choices,
                choicesLength: data.choices ? data.choices.length : 0,
                hasMessage: data.choices && data.choices[0] && !!data.choices[0].message
            });

            if (data.choices && data.choices[0] && data.choices[0].message) {
                const responseText = data.choices[0].message.content;
                console.log('Successfully extracted response text, length:', responseText.length);
                return responseText;
            } else {
                console.error('Invalid API response structure:', data);
                throw new Error('No content returned from OpenAI API');
            }
        }

        // Helper function to convert file to base64
        async function fileToBase64(file) {
            return new Promise((resolve, reject) => {
                const reader = new FileReader();
                reader.onload = () => {
                    // Remove the data URL prefix to get just the base64 data
                    const base64 = reader.result.split(',')[1];
                    resolve(base64);
                };
                reader.onerror = reject;
                reader.readAsDataURL(file);
            });
        }

        // Keep the original function as backup
        async function callOpenAIAPI(prompt) {
            const requestBody = {
                model: OPENAI_MODEL,
                messages: [
                    {
                        role: "system",
                        content: "You are an expert data extraction specialist. Analyze the provided text and extract job position information into CSV format as requested."
                    },
                    {
                        role: "user",
                        content: prompt
                    }
                ],
                max_tokens: 4096,
                temperature: 0.3
            };

            const response = await fetch(OPENAI_API_URL, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${OPENAI_API_KEY}`
                },
                body: JSON.stringify(requestBody)
            });

            if (!response.ok) {
                throw new Error(`OpenAI API error: ${response.status} ${response.statusText}`);
            }

            const data = await response.json();

            if (data.choices && data.choices[0] && data.choices[0].message) {
                return data.choices[0].message.content;
            } else {
                throw new Error('No content returned from OpenAI API');
            }
        }

        function formatAIResponse(response) {
            console.log('Formatting AI response...');
            console.log('Raw response length:', response ? response.length : 'null/undefined');
            console.log('Raw response preview:', response ? response.substring(0, 300) + '...' : 'No response');

            // Clean up the response
            let csvData = response.trim();

            // Remove any markdown code block markers
            csvData = csvData.replace(/```csv\n?/g, '').replace(/```\n?/g, '');

            console.log('Cleaned CSV data length:', csvData.length);
            console.log('Cleaned CSV preview:', csvData.substring(0, 300) + '...');

            // Validate CSV format (but don't fail if validation is too strict)
            const isValidCSV = validateCSVFormat(csvData);
            if (!isValidCSV) {
                console.warn('CSV validation failed, but proceeding anyway');
                console.log('CSV data that failed validation:', csvData);
                showNotification('CSV format validation warning - data may need manual review', 'warning');
            } else {
                console.log('CSV validation passed');
            }

            console.log('CSV validation passed');
            return csvData;
        }

        // ===== 4. OUTPUT DISPLAY FUNCTIONS =====

        function displayMarkdownOutput(csvData) {
            console.log('Displaying CSV output...');
            console.log('CSV data length:', csvData ? csvData.length : 'null/undefined');
            console.log('CSV data preview:', csvData ? csvData.substring(0, 200) + '...' : 'No data');

            const outputElement = document.getElementById('csvOutput');
            if (!outputElement) {
                console.error('csvOutput element not found!');
                showNotification('Error: Could not find output display element', 'error');
                return;
            }

            // Set the CSV content
            outputElement.textContent = csvData || 'No CSV data generated';

            // Ensure the output element is visible with inline styles as backup
            outputElement.style.display = 'block';
            outputElement.style.minHeight = '100px';
            outputElement.style.border = '1px solid #ccc';
            outputElement.style.padding = '15px';
            outputElement.style.backgroundColor = '#f9f9f9';
            outputElement.style.fontFamily = 'monospace';
            outputElement.style.whiteSpace = 'pre-wrap';
            outputElement.style.overflow = 'auto';

            console.log('CSV content set in output element');

            // Show results section
            const resultsSection = document.getElementById('resultsSection');
            if (resultsSection) {
                resultsSection.style.display = 'block';
                resultsSection.style.visibility = 'visible';
                console.log('Results section made visible');
            } else {
                console.error('resultsSection element not found!');
            }

            // Create copy button functionality
            createCopyButton();

            // Create download button functionality
            createDownloadButton();

            // Scroll to results
            resultsSection.scrollIntoView({ behavior: 'smooth', block: 'start' });
        }

        function createCopyButton() {
            const copyBtn = document.getElementById('copyBtn');
            copyBtn.style.display = 'block';
            copyBtn.onclick = copyToClipboard;

            // Ensure button is in default state
            copyBtn.classList.remove('copied');
            if (copyBtn.fadeTimeout) {
                clearTimeout(copyBtn.fadeTimeout);
                copyBtn.fadeTimeout = null;
            }
        }

        function createDownloadButton() {
            const downloadBtn = document.getElementById('downloadBtn');
            if (downloadBtn) {
                downloadBtn.style.display = 'block';
                downloadBtn.onclick = downloadCSV;
                console.log('Download button activated');
            }
        }

        function downloadCSV() {
            console.log('Download CSV function called');

            if (!dataStore.processingResults) {
                showNotification('No CSV data available to download. Please process files first.', 'error');
                return;
            }

            try {
                // Create blob with CSV data
                const blob = new Blob([dataStore.processingResults], { type: 'text/csv;charset=utf-8;' });

                // Create download link
                const link = document.createElement('a');
                const url = URL.createObjectURL(blob);
                link.setAttribute('href', url);

                // Generate filename with timestamp
                const timestamp = new Date().toISOString().slice(0, 19).replace(/:/g, '-');
                const filename = `PAIP_positions_${timestamp}.csv`;
                link.setAttribute('download', filename);

                // Trigger download
                link.style.visibility = 'hidden';
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);

                // Clean up
                URL.revokeObjectURL(url);

                showNotification(`CSV file "${filename}" downloaded successfully!`, 'success');
                console.log('CSV download completed:', filename);

            } catch (error) {
                console.error('Download error:', error);
                showNotification('Error downloading CSV file: ' + error.message, 'error');
            }
        }

        async function copyToClipboard() {
            const csvData = dataStore.processingResults;
            const copyBtn = document.getElementById('copyBtn');

            if (!csvData) {
                showNotification('No CSV data to copy. Please process files first.', 'error');
                return;
            }

            try {
                await navigator.clipboard.writeText(csvData);

                // Show visual feedback
                showCopySuccess(copyBtn);
                showNotification('CSV data copied to clipboard successfully!', 'success');

            } catch (error) {
                // Fallback for older browsers
                const textArea = document.createElement('textarea');
                textArea.value = csvData;
                document.body.appendChild(textArea);
                textArea.select();
                document.execCommand('copy');
                document.body.removeChild(textArea);

                // Show visual feedback
                showCopySuccess(copyBtn);
                showNotification('CSV data copied to clipboard!', 'success');
            }
        }

        function showCopySuccess(copyBtn) {
            // Clear any existing timeout
            if (copyBtn.fadeTimeout) {
                clearTimeout(copyBtn.fadeTimeout);
            }

            // Add copied class immediately (green background + checkmark)
            copyBtn.classList.add('copied');

            // Set timeout to fade back to orange after 1 minute (60 seconds)
            copyBtn.fadeTimeout = setTimeout(() => {
                copyBtn.classList.remove('copied');
                copyBtn.fadeTimeout = null;
            }, 60000); // 60 seconds = 1 minute
        }

        // ===== 5. UTILITY FUNCTIONS =====

        function showNotification(message, type) {
            // Remove existing notifications
            const existingNotifications = document.querySelectorAll('.notification');
            existingNotifications.forEach(notification => notification.remove());

            const notification = document.createElement('div');
            notification.className = `notification ${type}`;
            notification.textContent = message;

            // Insert at the top of the container
            const container = document.querySelector('.container');
            container.insertBefore(notification, container.firstChild);

            // Auto-remove after 5 seconds
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.remove();
                }
            }, 5000);
        }

        function resetApplication() {
            // Clear data store
            initializeDataStore();

            // Reset UI
            document.getElementById('fileList').style.display = 'none';
            document.getElementById('fileItems').innerHTML = '';
            document.getElementById('resultsSection').style.display = 'none';
            document.getElementById('csvOutput').textContent = '';
            document.getElementById('fileInput').value = '';
            document.getElementById('positionCodes').value = '';

            // Reset copy button state
            const copyBtn = document.getElementById('copyBtn');
            copyBtn.classList.remove('copied');
            if (copyBtn.fadeTimeout) {
                clearTimeout(copyBtn.fadeTimeout);
                copyBtn.fadeTimeout = null;
            }

            // Reset process button
            updateProcessButtonState();

            // Clear notifications
            const notifications = document.querySelectorAll('.notification');
            notifications.forEach(notification => notification.remove());

            showNotification('Application reset successfully!', 'success');
        }

        function validateCSVFormat(csvText) {
            if (!csvText || csvText.trim().length === 0) {
                return false;
            }

            const lines = csvText.trim().split('\n');

            // Check if we have at least a header row
            if (lines.length < 1) {
                return false;
            }

            // Check if header contains required fields
            const header = lines[0].toLowerCase();
            const requiredFields = [
                'position_reference', 'designation', 'classification',
                'award', 'location', 'annual_salary', 'qualifications',
                'knowledge', 'skills_competencies', 'job_experiences', 'remarks'
            ];

            let missingFields = [];
            for (let field of requiredFields) {
                if (!header.includes(field)) {
                    missingFields.push(field);
                }
            }

            if (missingFields.length > 0) {
                console.warn(`Missing required fields: ${missingFields.join(', ')}`);
                // Still return true but log the warning - AI might use slightly different field names
            }

            // Check if we have data rows (at least header + 1 data row)
            if (lines.length < 2) {
                console.warn('CSV appears to have no data rows');
            }

            return true;
        }

        // ===== 6. ERROR HANDLING FUNCTIONS =====

        function handleFileError(filename, error) {
            updateFileStatus(filename, `Error: ${error.message}`);
            showNotification(`Error processing file "${filename}": ${error.message}`, 'error');
        }

        function handleAPIError(error) {
            console.error('API Error:', error);
            showNotification(`API Error: ${error.message}`, 'error');
        }

        function handleExtractionError(filename, error) {
            console.error(`Extraction error for ${filename}:`, error);
            updateFileStatus(filename, `Extraction failed: ${error.message}`);
            showNotification(`Failed to extract text from "${filename}": ${error.message}`, 'error');
        }

        // ===== 7. UI STATE MANAGEMENT FUNCTIONS =====

        function updateUIState(state) {
            const processBtn = document.getElementById('processBtn');
            const processSpinner = document.getElementById('processSpinner');
            const processButtonText = document.getElementById('processButtonText');

            switch (state) {
                case 'ready':
                    processBtn.disabled = false;
                    processButtonText.textContent = 'Process All Files to CSV';
                    processSpinner.style.display = 'none';
                    break;
                case 'processing':
                    processBtn.disabled = true;
                    processButtonText.textContent = 'Processing...';
                    processSpinner.style.display = 'inline-block';
                    break;
                case 'disabled':
                    processBtn.disabled = true;
                    processButtonText.textContent = 'Upload PDF files first';
                    processSpinner.style.display = 'none';
                    break;
            }
        }

        function updateProcessButtonState() {
            const hasFiles = dataStore.extractedTexts.size > 0;
            const allCompleted = Array.from(dataStore.fileStatuses.values())
                .every(status => status.includes('completed'));

            if (hasFiles && allCompleted) {
                updateUIState('ready');
            } else if (hasFiles) {
                updateUIState('processing');
            } else {
                updateUIState('disabled');
            }
        }

        function showLoadingState() {
            document.getElementById('loadingOverlay').style.display = 'flex';
            document.getElementById('loadingTitle').textContent = 'Processing with AI...';
            document.getElementById('loadingMessage').textContent = 'Analyzing PDF content and generating CSV format. This may take a few minutes.';
            updateUIState('processing');
        }

        function hideLoadingState() {
            document.getElementById('loadingOverlay').style.display = 'none';
            updateUIState('ready');
        }

        // ===== POSITION CODES INPUT VALIDATION =====

        function validatePositionCodesInput() {
            const input = document.getElementById('positionCodes');
            const preview = document.getElementById('positionCodesPreview');
            const value = input.value.trim();

            if (value === '') {
                preview.style.display = 'none';
                return;
            }

            const positions = value.split(',').map(code => code.trim()).filter(code => code.length > 0);

            if (positions.length > 0) {
                preview.innerHTML = `<strong>🎯 Will extract ${positions.length} position(s):</strong> ${positions.join(', ')}`;
                preview.style.display = 'block';
                preview.style.borderColor = 'rgba(76, 175, 80, 0.3)';
                preview.style.background = 'rgba(76, 175, 80, 0.1)';
            } else {
                preview.innerHTML = '<strong>⚠️ Invalid format:</strong> Please enter position codes separated by commas';
                preview.style.display = 'block';
                preview.style.borderColor = 'rgba(244, 67, 54, 0.3)';
                preview.style.background = 'rgba(244, 67, 54, 0.1)';
            }
        }

        function getPositionCodesArray() {
            const input = document.getElementById('positionCodes').value.trim();
            if (!input) return [];

            return input.split(',')
                       .map(code => code.trim())
                       .filter(code => code.length > 0);
        }

        // ===== NAVIGATION FUNCTIONALITY =====

        function initializeNavigation() {
            const hamburger = document.getElementById('hamburger');
            const navMenu = document.getElementById('nav-menu');

            if (hamburger && navMenu) {
                hamburger.addEventListener('click', function() {
                    hamburger.classList.toggle('active');
                    navMenu.classList.toggle('active');
                });

                // Close menu when clicking on a link
                const navLinks = document.querySelectorAll('.nav-link');
                navLinks.forEach(link => {
                    link.addEventListener('click', function() {
                        hamburger.classList.remove('active');
                        navMenu.classList.remove('active');
                    });
                });

                // Close menu when clicking outside
                document.addEventListener('click', function(event) {
                    const isClickInsideNav = navMenu.contains(event.target) || hamburger.contains(event.target);
                    if (!isClickInsideNav && navMenu.classList.contains('active')) {
                        hamburger.classList.remove('active');
                        navMenu.classList.remove('active');
                    }
                });

                // Handle window resize
                window.addEventListener('resize', function() {
                    if (window.innerWidth > 768) {
                        hamburger.classList.remove('active');
                        navMenu.classList.remove('active');
                    }
                });
            }
        }

        // ===== INITIALIZATION =====

        document.addEventListener('DOMContentLoaded', function() {
            console.log('PDF to CSV Processor - Initializing...');

            // Initialize navigation
            initializeNavigation();

            // Initialize data store
            initializeDataStore();

            // Initialize drag and drop
            initializeDragDrop();

            // Set initial UI state
            updateUIState('disabled');

            console.log('PDF to CSV Processor - Ready!');
        });
    </script>
</body>
</html>