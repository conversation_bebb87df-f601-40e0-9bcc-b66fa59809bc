<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Google Vision API Handwriting Text Extractor</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #4285f4, #34a853);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
            font-weight: 700;
        }

        .header p {
            opacity: 0.9;
            font-size: 1.1rem;
        }

        .main-content {
            padding: 40px;
        }

        .config-section {
            background: #f8fafc;
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 30px;
            border: 2px solid #e2e8f0;
        }

        .config-section h3 {
            color: #1e293b;
            margin-bottom: 20px;
            font-size: 1.3rem;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .config-section h3::before {
            content: "🔧";
            font-size: 1.2rem;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #374151;
        }

        .form-group input, .form-group select {
            width: 100%;
            padding: 12px 16px;
            border: 2px solid #d1d5db;
            border-radius: 10px;
            font-size: 1rem;
            transition: all 0.3s ease;
            background: white;
        }

        .form-group input:focus, .form-group select:focus {
            outline: none;
            border-color: #4285f4;
            box-shadow: 0 0 0 3px rgba(66, 133, 244, 0.1);
        }

        .info-box {
            background: #e3f2fd;
            border: 2px solid #90caf9;
            border-radius: 10px;
            padding: 15px;
            margin-top: 15px;
            font-size: 0.9rem;
            color: #1565c0;
        }

        .detection-options {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-bottom: 20px;
        }

        .option-card {
            background: white;
            border: 2px solid #e2e8f0;
            border-radius: 12px;
            padding: 20px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .option-card:hover {
            border-color: #4285f4;
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(66, 133, 244, 0.2);
        }

        .option-card.selected {
            border-color: #4285f4;
            background: #f8faff;
        }

        .option-card h4 {
            color: #1e293b;
            margin-bottom: 8px;
            font-size: 1.1rem;
        }

        .option-card p {
            color: #64748b;
            font-size: 0.9rem;
            line-height: 1.4;
        }

        .upload-section {
            text-align: center;
            margin: 30px 0;
        }

        .file-upload {
            position: relative;
            display: inline-block;
            cursor: pointer;
            background: linear-gradient(135deg, #34a853, #137333);
            color: white;
            padding: 15px 30px;
            border-radius: 12px;
            font-size: 1.1rem;
            font-weight: 600;
            transition: all 0.3s ease;
            border: none;
            box-shadow: 0 4px 15px rgba(52, 168, 83, 0.3);
        }

        .file-upload:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(52, 168, 83, 0.4);
        }

        .file-upload input[type="file"] {
            position: absolute;
            left: -9999px;
        }

        .process-btn {
            background: linear-gradient(135deg, #ea4335, #d33b2c);
            color: white;
            border: none;
            padding: 15px 40px;
            border-radius: 12px;
            font-size: 1.1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 20px 10px;
            box-shadow: 0 4px 15px rgba(234, 67, 53, 0.3);
        }

        .process-btn:hover:not(:disabled) {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(234, 67, 53, 0.4);
        }

        .process-btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .preview-section {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin-top: 30px;
        }

        .preview-card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
            border: 2px solid #f1f5f9;
        }

        .preview-card h4 {
            color: #1e293b;
            margin-bottom: 15px;
            font-size: 1.2rem;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .image-preview {
            max-width: 100%;
            height: auto;
            border-radius: 10px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }

        .extracted-text {
            background: #f8fafc;
            border: 2px solid #e2e8f0;
            border-radius: 10px;
            padding: 20px;
            min-height: 200px;
            font-family: 'Courier New', monospace;
            font-size: 0.95rem;
            line-height: 1.6;
            white-space: pre-wrap;
            word-wrap: break-word;
        }

        .confidence-info {
            background: #f0f9ff;
            border: 1px solid #0ea5e9;
            border-radius: 8px;
            padding: 12px;
            margin-bottom: 15px;
            font-size: 0.9rem;
            color: #0c4a6e;
        }

        .loading {
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 40px;
            color: #6b7280;
        }

        .spinner {
            width: 40px;
            height: 40px;
            border: 4px solid #f3f4f6;
            border-top: 4px solid #4285f4;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-right: 15px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .error {
            background: #fef2f2;
            border: 2px solid #fca5a5;
            color: #dc2626;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
        }

        .success {
            background: #f0fdf4;
            border: 2px solid #86efac;
            color: #16a34a;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
        }

        .copy-btn {
            background: #4285f4;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 6px;
            font-size: 0.9rem;
            cursor: pointer;
            margin-top: 10px;
            transition: all 0.3s ease;
        }

        .copy-btn:hover {
            background: #3367d6;
        }

        .language-selector {
            margin-bottom: 20px;
        }

        .language-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 10px;
            margin-top: 10px;
        }

        .language-option {
            padding: 8px 12px;
            border: 2px solid #e2e8f0;
            border-radius: 8px;
            background: white;
            cursor: pointer;
            text-align: center;
            transition: all 0.3s ease;
            font-size: 0.9rem;
        }

        .language-option:hover {
            border-color: #4285f4;
        }

        .language-option.selected {
            border-color: #4285f4;
            background: #f8faff;
            color: #4285f4;
            font-weight: 600;
        }

        @media (max-width: 768px) {
            .preview-section {
                grid-template-columns: 1fr;
            }

            .detection-options {
                grid-template-columns: 1fr;
            }

            .header h1 {
                font-size: 2rem;
            }

            .main-content {
                padding: 20px;
            }
        }
    </style>
</head>
    <div class="container">
        <div class="header">
            <h1>📝 Google Vision API Text Extractor</h1>
            <p>Extract handwritten and printed text from images using Google Cloud Vision API</p>
        </div>

        <div class="main-content">
            <div class="config-section">
                <h3>API Configuration</h3>
                <div class="form-group">
                    <label for="apiKey">Google Cloud Vision API Key:</label>
                    <input type="password" id="apiKey" placeholder="your-api-key" />
                    <div class="info-box">
                        💡 <strong>How to get your API key:</strong><br>
                        1. Go to <a href="https://console.cloud.google.com/" target="_blank">Google Cloud Console</a><br>
                        2. Enable the Vision API for your project<br>
                        3. Create credentials (API Key) in the Credentials section<br>
                        4. Restrict the key to Vision API for security
                    </div>
                </div>

                <div class="form-group">
                    <label>Detection Type:</label>
                    <div class="detection-options">
                        <div class="option-card selected" data-type="DOCUMENT_TEXT_DETECTION">
                            <h4>📄 Document Text Detection</h4>
                            <p>Best for dense text, documents, and handwritten content. Provides structured output with word-level confidence.</p>
                        </div>
                        <div class="option-card" data-type="TEXT_DETECTION">
                            <h4>🔍 General Text Detection</h4>
                            <p>Good for signs, labels, and sparse text in images. Works well for text in natural scenes.</p>
                        </div>
                    </div>
                </div>

                <div class="form-group language-selector">
                    <label>Language Hints (Optional):</label>
                    <div class="language-grid">
                        <div class="language-option selected" data-lang="">Auto-detect</div>
                        <div class="language-option" data-lang="en-t-i0-handwrit">English Handwriting</div>
                        <div class="language-option" data-lang="en">English</div>
                        <div class="language-option" data-lang="es">Spanish</div>
                        <div class="language-option" data-lang="fr">French</div>
                        <div class="language-option" data-lang="de">German</div>
                        <div class="language-option" data-lang="zh">Chinese</div>
                        <div class="language-option" data-lang="ja">Japanese</div>
                    </div>
                </div>
            </div>

            <div class="upload-section">
                <label for="imageUpload" class="file-upload">
                    📤 Choose Image File
                    <input type="file" id="imageUpload" accept="image/*" />
                </label>
                <br>
                <button id="processBtn" class="process-btn" disabled>🔍 Extract Text</button>
            </div>

            <div id="status"></div>

            <div class="preview-section" id="previewSection" style="display: none;">
                <div class="preview-card">
                    <h4>📷 Uploaded Image</h4>
                    <img id="imagePreview" class="image-preview" alt="Uploaded image" />
                </div>
                <div class="preview-card">
                    <h4>📝 Extracted Text</h4>
                    <div id="confidenceInfo" class="confidence-info" style="display: none;"></div>
                    <div id="extractedText" class="extracted-text">No text extracted yet...</div>
                    <button id="copyBtn" class="copy-btn">📋 Copy Text</button>
                </div>
            </div>
        </div>
    </div>

    <script>
        class VisionAPIExtractor {
            constructor() {
                this.initializeElements();
                this.attachEventListeners();
                this.selectedFile = null;
                this.detectionType = 'DOCUMENT_TEXT_DETECTION';
                this.languageHint = '';
            }

            initializeElements() {
                this.apiKeyInput = document.getElementById('apiKey');
                this.imageUpload = document.getElementById('imageUpload');
                this.processBtn = document.getElementById('processBtn');
                this.statusDiv = document.getElementById('status');
                this.previewSection = document.getElementById('previewSection');
                this.imagePreview = document.getElementById('imagePreview');
                this.extractedText = document.getElementById('extractedText');
                this.confidenceInfo = document.getElementById('confidenceInfo');
                this.copyBtn = document.getElementById('copyBtn');
                this.detectionOptions = document.querySelectorAll('.option-card');
                this.languageOptions = document.querySelectorAll('.language-option');
            }

            attachEventListeners() {
                this.imageUpload.addEventListener('change', (e) => this.handleFileSelect(e));
                this.processBtn.addEventListener('click', () => this.processImage());
                this.copyBtn.addEventListener('click', () => this.copyText());

                // Enable process button when required fields are filled
                this.apiKeyInput.addEventListener('input', () => this.validateForm());

                // Detection type selection
                this.detectionOptions.forEach(option => {
                    option.addEventListener('click', () => {
                        this.detectionOptions.forEach(opt => opt.classList.remove('selected'));
                        option.classList.add('selected');
                        this.detectionType = option.dataset.type;
                    });
                });

                // Language selection
                this.languageOptions.forEach(option => {
                    option.addEventListener('click', () => {
                        this.languageOptions.forEach(opt => opt.classList.remove('selected'));
                        option.classList.add('selected');
                        this.languageHint = option.dataset.lang;
                    });
                });
            }

            validateForm() {
                const isValid = this.apiKeyInput.value.trim() && this.selectedFile;
                this.processBtn.disabled = !isValid;
            }

            handleFileSelect(event) {
                const file = event.target.files[0];
                if (!file) return;

                if (!file.type.startsWith('image/')) {
                    this.showError('Please select a valid image file.');
                    return;
                }

                // Check file size (Vision API has a 20MB limit)
                if (file.size > 20 * 1024 * 1024) {
                    this.showError('Image file is too large. Please select an image smaller than 20MB.');
                    return;
                }

                this.selectedFile = file;
                this.validateForm();

                // Preview the image
                const reader = new FileReader();
                reader.onload = (e) => {
                    this.imagePreview.src = e.target.result;
                    this.previewSection.style.display = 'grid';
                };
                reader.readAsDataURL(file);

                this.clearStatus();
            }

            async fileToBase64(file) {
                return new Promise((resolve, reject) => {
                    const reader = new FileReader();
                    reader.onload = () => {
                        const base64 = reader.result.split(',')[1];
                        resolve(base64);
                    };
                    reader.onerror = reject;
                    reader.readAsDataURL(file);
                });
            }

            async processImage() {
                if (!this.selectedFile) {
                    this.showError('Please select an image file first.');
                    return;
                }

                this.showLoading('Processing image with Google Vision API...');
                this.processBtn.disabled = true;

                try {
                    const base64Content = await this.fileToBase64(this.selectedFile);
                    const apiKey = this.apiKeyInput.value.trim();

                    // Construct the request
                    const requestBody = {
                        requests: [{
                            image: {
                                content: base64Content
                            },
                            features: [{
                                type: this.detectionType,
                                maxResults: 1
                            }]
                        }]
                    };

                    // Add language hints if specified
                    if (this.languageHint) {
                        requestBody.requests[0].imageContext = {
                            languageHints: [this.languageHint]
                        };
                    }

                    const endpoint = `https://vision.googleapis.com/v1/images:annotate?key=${apiKey}`;

                    const response = await fetch(endpoint, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify(requestBody)
                    });

                    if (!response.ok) {
                        const errorData = await response.json();
                        const errorMessage = errorData.error?.message || `HTTP ${response.status}: ${response.statusText}`;
                        throw new Error(errorMessage);
                    }

                    const result = await response.json();
                    this.displayResults(result);

                } catch (error) {
                    console.error('Processing error:', error);
                    this.showError(`Failed to process image: ${error.message}`);
                } finally {
                    this.processBtn.disabled = false;
                }
            }

            displayResults(result) {
                try {
                    if (!result.responses || result.responses.length === 0) {
                        this.showError('No response received from the API.');
                        return;
                    }

                    const response = result.responses[0];

                    if (response.error) {
                        this.showError(`API Error: ${response.error.message}`);
                        return;
                    }

                    let extractedText = '';
                    let confidenceScore = 0;
                    let wordCount = 0;

                    if (this.detectionType === 'DOCUMENT_TEXT_DETECTION' && response.fullTextAnnotation) {
                        // Use fullTextAnnotation for document text detection
                        extractedText = response.fullTextAnnotation.text || '';

                        // Calculate average confidence from pages
                        if (response.fullTextAnnotation.pages) {
                            let totalConfidence = 0;
                            let totalWords = 0;

                            response.fullTextAnnotation.pages.forEach(page => {
                                if (page.blocks) {
                                    page.blocks.forEach(block => {
                                        if (block.paragraphs) {
                                            block.paragraphs.forEach(paragraph => {
                                                if (paragraph.words) {
                                                    paragraph.words.forEach(word => {
                                                        if (word.confidence !== undefined) {
                                                            totalConfidence += word.confidence;
                                                            totalWords++;
                                                        }
                                                    });
                                                }
                                            });
                                        }
                                    });
                                }
                            });

                            if (totalWords > 0) {
                                confidenceScore = totalConfidence / totalWords;
                                wordCount = totalWords;
                            }
                        }
                    } else if (response.textAnnotations && response.textAnnotations.length > 0) {
                        // Use textAnnotations for regular text detection
                        extractedText = response.textAnnotations[0].description || '';

                        // Calculate average confidence from individual text annotations
                        let totalConfidence = 0;
                        let count = 0;

                        response.textAnnotations.forEach(annotation => {
                            if (annotation.confidence !== undefined) {
                                totalConfidence += annotation.confidence;
                                count++;
                            }
                        });

                        if (count > 0) {
                            confidenceScore = totalConfidence / count;
                        }
                        wordCount = response.textAnnotations.length - 1; // Subtract 1 for the full text annotation
                    }

                    if (!extractedText.trim()) {
                        this.showError('No text could be extracted from the image. Please try with a clearer image or ensure the text is legible.');
                        return;
                    }

                    // Display the extracted text
                    this.extractedText.textContent = extractedText;

                    // Display confidence information
                    if (confidenceScore > 0) {
                        const confidencePercent = Math.round(confidenceScore * 100);
                        this.confidenceInfo.innerHTML = `
                            <strong>Extraction Results:</strong><br>
                            📊 Confidence: ${confidencePercent}%<br>
                            📝 Words detected: ${wordCount}<br>
                            📏 Characters: ${extractedText.length}
                        `;
                        this.confidenceInfo.style.display = 'block';
                    } else {
                        this.confidenceInfo.style.display = 'none';
                    }

                    this.showSuccess(`Successfully extracted ${extractedText.length} characters of text!`);

                } catch (error) {
                    console.error('Result processing error:', error);
                    this.showError('Failed to process the extraction results.');
                }
            }

            copyText() {
                const text = this.extractedText.textContent;
                if (!text || text === 'No text extracted yet...') {
                    this.showError('No text to copy.');
                    return;
                }

                navigator.clipboard.writeText(text).then(() => {
                    const originalText = this.copyBtn.textContent;
                    this.copyBtn.textContent = '✅ Copied!';
                    setTimeout(() => {
                        this.copyBtn.textContent = originalText;
                    }, 2000);
                }).catch(() => {
                    this.showError('Failed to copy text to clipboard.');
                });
            }

            showLoading(message) {
                this.statusDiv.innerHTML = `
                    <div class="loading">
                        <div class="spinner"></div>
                        ${message}
                    </div>
                `;
            }

            showError(message) {
                this.statusDiv.innerHTML = `<div class="error">❌ ${message}</div>`;
            }

            showSuccess(message) {
                this.statusDiv.innerHTML = `<div class="success">✅ ${message}</div>`;
            }

            clearStatus() {
                this.statusDiv.innerHTML = '';
            }
        }

        // Initialize the application
        document.addEventListener('DOMContentLoaded', () => {
            new VisionAPIExtractor();
        });
    </script>
</body>
</html>
