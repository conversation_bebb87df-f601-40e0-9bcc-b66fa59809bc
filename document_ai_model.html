<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Document Text Extractor</title>
    <!-- Tailwind CSS for styling -->
    <script src="https://cdn.tailwindcss.com"></script>
    <!-- Google Fonts: Inter -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    <style>
        /* Custom styles for the application */
        body {
            font-family: 'Inter', sans-serif;
            background-color: #f3f4f6; /* A light gray background */
        }
        /* Style for the file input button */
        input[type="file"]::file-selector-button {
            background-color: #4f46e5;
            color: white;
            border: none;
            padding: 0.5rem 1rem;
            border-radius: 0.375rem;
            cursor: pointer;
            transition: background-color 0.2s;
        }
        input[type="file"]::file-selector-button:hover {
            background-color: #4338ca;
        }
    </style>
</head>
<body class="bg-gray-100 text-gray-800 flex items-center justify-center min-h-screen p-4">

    <div class="container mx-auto max-w-4xl w-full bg-white rounded-2xl shadow-xl p-6 md:p-10">
        
        <!-- Header Section -->
        <header class="text-center mb-8">
            <h1 class="text-3xl md:text-4xl font-bold text-indigo-600">Document & Handwriting Extractor</h1>
            <p class="text-gray-500 mt-2">Upload an image to read its printed and handwritten text using AI.</p>
        </header>

        <main>
            <!-- File Upload Section -->
            <div class="mb-6">
                <label for="image-upload" class="block text-lg font-medium text-gray-700 mb-2">1. Choose an Image</label>
                <input type="file" id="image-upload" accept="image/*" class="w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-lg file:border-0 file:text-sm file:font-semibold file:bg-indigo-50 file:text-indigo-700 hover:file:bg-indigo-100 transition-colors duration-200">
            </div>

            <!-- Image Preview and Action Button -->
            <div id="preview-container" class="mb-6 hidden">
                <h2 class="text-lg font-medium text-gray-700 mb-2">2. Preview & Extract</h2>
                <div class="border-2 border-dashed border-gray-300 rounded-xl p-4 text-center">
                    <img id="image-preview" src="#" alt="Image Preview" class="max-w-full max-h-96 mx-auto rounded-lg shadow-md mb-4"/>
                    <button id="extract-button" class="bg-indigo-600 text-white font-bold py-3 px-8 rounded-lg hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-transform transform hover:scale-105 duration-200 disabled:bg-gray-400 disabled:cursor-not-allowed disabled:scale-100" disabled>
                        Extract Text
                    </button>
                </div>
            </div>

            <!-- Results Section -->
            <div id="result-container" class="mt-8 hidden">
                <h2 class="text-lg font-medium text-gray-700 mb-2">3. Extracted Text</h2>
                <!-- Loading Spinner -->
                <div id="loader" class="flex items-center justify-center py-10 hidden">
                    <svg class="animate-spin -ml-1 mr-3 h-8 w-8 text-indigo-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                        <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    <span class="text-gray-600">Analyzing image...</span>
                </div>
                <!-- Output Area -->
                <div id="output" class="bg-gray-50 p-4 border border-gray-200 rounded-lg whitespace-pre-wrap text-gray-700 min-h-[100px] shadow-inner"></div>
                <!-- Error Message Box -->
                <div id="error-message" class="mt-4 p-4 bg-red-100 text-red-700 border border-red-300 rounded-lg hidden"></div>
            </div>
        </main>
    </div>

    <script>
        // Get references to all the necessary HTML elements
        const imageUpload = document.getElementById('image-upload');
        const imagePreview = document.getElementById('image-preview');
        const previewContainer = document.getElementById('preview-container');
        const extractButton = document.getElementById('extract-button');
        const resultContainer = document.getElementById('result-container');
        const loader = document.getElementById('loader');
        const output = document.getElementById('output');
        const errorMessage = document.getElementById('error-message');

        // Store the base64 representation of the uploaded image
        let base64ImageData = null;
        let imageMimeType = null;

        // --- Event Listener for File Input ---
        // This function runs when the user selects a file.
        imageUpload.addEventListener('change', (event) => {
            const file = event.target.files[0];
            if (!file) return;

            // Use FileReader to read the file as a data URL (base64)
            const reader = new FileReader();
            reader.onload = (e) => {
                const imageDataUrl = e.target.result;
                imagePreview.src = imageDataUrl;
                
                // Extract the base64 data and mime type from the data URL
                const parts = imageDataUrl.split(',');
                imageMimeType = parts[0].match(/:(.*?);/)[1];
                base64ImageData = parts[1];

                // Show the preview and enable the extract button
                previewContainer.classList.remove('hidden');
                extractButton.disabled = false;
                resultContainer.classList.add('hidden');
                output.textContent = '';
                errorMessage.classList.add('hidden');
            };
            reader.readAsDataURL(file);
        });

        // --- Event Listener for Extract Button ---
        // This function runs when the "Extract Text" button is clicked.
        extractButton.addEventListener('click', async () => {
            if (!base64ImageData) {
                showError("Please select an image first.");
                return;
            }

            // Show the results section and the loading spinner
            resultContainer.classList.remove('hidden');
            loader.classList.remove('hidden');
            output.textContent = '';
            errorMessage.classList.add('hidden');
            extractButton.disabled = true; // Prevent multiple clicks

            try {
                // This is the prompt for the AI model
                const prompt = "Extract all text from this image, including any handwritten notes. Preserve the original formatting as much as possible.";

                // Prepare the data payload for the Gemini API
                const payload = {
                    contents: [{
                        parts: [
                            { text: prompt },
                            {
                                inlineData: {
                                    mimeType: imageMimeType,
                                    data: base64ImageData
                                }
                            }
                        ]
                    }]
                };
                
                // The API key is left empty as it will be provided by the environment
                const apiKey = ""; 
                const apiUrl = `https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash-preview-05-20:generateContent?key=${apiKey}`;

                // Make the API call using fetch
                const response = await fetch(apiUrl, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(payload)
                });
                
                if (!response.ok) {
                    const errorData = await response.json();
                    throw new Error(errorData.error?.message || `API request failed with status ${response.status}`);
                }

                const result = await response.json();
                
                // Process the response and extract the text
                if (result.candidates && result.candidates.length > 0 &&
                    result.candidates[0].content && result.candidates[0].content.parts &&
                    result.candidates[0].content.parts.length > 0) {
                    const extractedText = result.candidates[0].content.parts[0].text;
                    output.textContent = extractedText;
                } else {
                    // Handle cases where the response structure is unexpected
                    throw new Error("Could not find extracted text in the API response.");
                }

            } catch (error) {
                console.error("Error during text extraction:", error);
                showError(`An error occurred: ${error.message}`);
            } finally {
                // Hide the loader and re-enable the button
                loader.classList.add('hidden');
                extractButton.disabled = false;
            }
        });

        // --- Helper Function to Show Errors ---
        function showError(message) {
            errorMessage.textContent = message;
            errorMessage.classList.remove('hidden');
            loader.classList.add('hidden');
        }
    </script>
</body>
</html>
